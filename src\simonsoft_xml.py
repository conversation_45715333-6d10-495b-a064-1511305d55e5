"""
Simonsoft XML Translation Script

This script processes XML files (e.g., Simonsoft service manuals) and translates only the tags and attributes that require translation, following Simonsoft documentation rules. It is designed to integrate with the app's logging and translation infrastructure, and to be as efficient as possible with translation API calls.
"""


import os
import sys
import logging
from lxml import etree
from typing import List, Dict, Tuple
import time
from src.textual import TextTranslator
from utils.core import get_logger, Mode

logger = get_logger(__file__)

# Simonsoft translation rules: do not translate if markfortrans="no", type="notrans", etc.
EXCLUDE_ATTRS = {"markfortrans": "no", "type": "notrans"}

class SimonsoftXMLTranslator:
    def __init__(self, verify=True):
        self.translator = TextTranslator(verify=verify)
        self.translate_calls = 0

    @property
    def supported_languages(self):
        return self.translator.supported_languages

    def _should_translate_element(self, elem: etree.Element) -> bool:
        # Exclude by markfortrans, type, etc.
        for k, v in EXCLUDE_ATTRS.items():
            if elem.get(k) == v:
                logger.debug(f"Skipping element <{elem.tag}> due to {k}={v}")
                return False
        # Exclude by tstatus=Released (with or without namespace) because already translated in another project
        tstatus = elem.get('{http://www.simonsoft.se/ns/cms}tstatus') or elem.get('cms:tstatus')
        if tstatus == 'Released':
            logger.debug(f"Skipping element <{elem.tag}> due to cms:tstatus=Released")
            return False
        return True

    def _collect_translatables(self, root: etree.Element) -> List[Tuple[etree.Element, str]]:
        text_nodes = []  # (element, text_type) where text_type is 'text' or 'tail'
        for elem in root.iter():
            # Skip processing instructions (like <?PubTbl cell ...?>)
            if isinstance(elem.tag, type(etree.ProcessingInstruction)):
                continue
            # Skip comments
            if isinstance(elem.tag, type(etree.Comment)):
                continue
            # Skip if tag is a callable (processing instruction or comment)
            if callable(elem.tag):
                continue
            
            if not self._should_translate_element(elem):
                continue
            # Text nodes
            if elem.text and elem.text.strip():
                text_nodes.append((elem, 'text'))
            if elem.tail and elem.tail.strip():
                text_nodes.append((elem, 'tail'))
        return text_nodes

    # TODO implement real batch processing
    def translate_xml(self, xml_input: str, source_lang: str, target_langs: List[str], mode: Mode = Mode.STANDARD_PROOFREAD_BATCH, batch_size: int = 500) -> Dict[str, str]:
        logger.info(f"Processing XML input: {xml_input[:100]}..." if len(xml_input) > 100 else f"Processing XML input: {xml_input}")
        xml_preamble = ''
        try:
            # Input is a string, preserve preamble
            import re
            match = re.search(r'^(.*?)(<document[\s>])', xml_input, re.DOTALL)
            if match:
                xml_preamble = match.group(1)
                xml_body = xml_input[len(xml_preamble):]
            else:
                xml_body = xml_input
            parser = etree.XMLParser(dtd_validation=False, load_dtd=False, recover=True)
            root = etree.fromstring(xml_body.encode("utf-8"), parser=parser)
        except Exception as e:
            logger.error(f"Error parsing XML input: {e}")
            raise Exception(f"Error parsing XML input: {e}")
        text_nodes = self._collect_translatables(root)
        logger.info(f"Collected {len(text_nodes)} text nodes for translation.")

        # Prepare batches for translation
        texts_to_translate = {}
        node_keys = []
        for i, (elem, text_type) in enumerate(text_nodes):
            key = f"text_{i}"
            value = elem.text if text_type == 'text' else elem.tail
            texts_to_translate[key] = value
            node_keys.append((key, elem, text_type))

        # Batching logic
        all_keys = list(texts_to_translate.keys())
        translations = {lang: {} for lang in target_langs}
        total_batches = (len(all_keys) + batch_size - 1) // batch_size
        batch_iter = range(0, len(all_keys), batch_size)
        for batch_num, batch_start in enumerate(batch_iter, 1):
            batch_keys = all_keys[batch_start:batch_start+batch_size]
            logger.info(f"[PROGRESS] Starting batch {batch_num}/{total_batches} (nodes {batch_start+1}-{batch_start+len(batch_keys)})")
            batch_texts = {k: texts_to_translate[k] for k in batch_keys}
            batch_time_start = time.time()
            try:
                batch_response = self.translator.translate(batch_texts, source_lang, target_langs, mode=mode)
                if "text" in batch_response:
                    batch_result = batch_response["text"]
                else:
                    batch_result = batch_response
                self.translate_calls += 1
                logger.info(f"[PROGRESS] Finished batch {batch_num}/{total_batches} (API call {self.translate_calls}, nodes {batch_start+1}-{batch_start+len(batch_keys)}), elapsed {time.time()-batch_time_start:.2f}s.")
                for lang in target_langs:
                    for k, v in batch_result.items():
                        if lang in v:
                            translations[lang][k] = v[lang]
            except Exception as e:
                logger.error(f"Translation batch failed: {e}")
                raise Exception(f"Translation batch failed: {e}")
            if batch_num % 10 == 0:
                logger.info(f"[PROGRESS] {batch_num} batches processed out of {total_batches}.")

        # Insert translations back
        results = {"xml": {}}
        for lang in target_langs:
            for key, elem, info in node_keys:
                translated = translations[lang].get(key, None)
                if translated is not None:
                    if info == 'text':
                        elem.text = translated
                    else:
                        elem.tail = translated
            # Output XML string
            xml_bytes = etree.tostring(root, encoding='utf-8', pretty_print=True, xml_declaration=False)
            # Prepend preserved preamble (xml declaration, DOCTYPE, comments, etc.)
            results["xml"][lang] = (xml_preamble or '') + xml_bytes.decode('utf-8')
        return results

if __name__ == "__main__":
    import sys
    if len(sys.argv) < 4:
        print("Usage: python simonsoft_xml.py <xml_file_path> <source_lang> <target_lang1> [<target_lang2> ...]")
        print("Note: This script reads the XML file and processes the content as a string.")
        sys.exit(1)
    xml_file_path = sys.argv[1]
    source_lang = sys.argv[2]
    target_langs = sys.argv[3:]
    
    # Read the XML file content
    try:
        with open(xml_file_path, 'r', encoding='utf-8') as f:
            xml_content = f.read()
    except Exception as e:
        print(f"Error reading file '{xml_file_path}': {e}")
        sys.exit(1)
    
    translator = SimonsoftXMLTranslator()
    results = translator.translate_xml(xml_content, source_lang, target_langs)
    for lang, xml in results["xml"].items():
        out_path = f"{os.path.splitext(xml_file_path)[0]}_translated_{lang}.xml"
        with open(out_path, "w", encoding="utf-8") as f:
            f.write(xml)
        print(f"Saved translated XML for {lang} to {out_path}")
