[common]
region = westeurope
host =
llm_endpoint = https://epr002-oai-cmn-stg-weu-001.openai.azure.com/
llm_name = gpt-4.1-mini
api_version = 2025-01-01-preview
log_level = INFO

# Azure OpenAI Embedder
embedder_endpoint = https://epr002-oai-cmn-stg-weu-001.openai.azure.com/
embedder_deployment_name = embedding-test-rag
embedder_version = 2023-12-01-preview

# Azure Search AI
search_endpoint = https://epr002-srch-cmn-all-weu-001.search.windows.net
search_index = translation-groundtruth-pride

[text_translation]
endpoint = https://epr002-trsl-cmn-stg-weu-001.cognitiveservices.azure.com/

[document_translation]
endpoint = https://epr002-trsl-cmn-stg-weu-001.cognitiveservices.azure.com/