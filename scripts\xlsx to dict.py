import pandas as pd
import openpyxl
import json
import logging

# <PERSON>fi<PERSON>ra il logging per salvare i log in un file
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    filename="process_log.txt",  # Nome del file di log
    filemode="w"  # Sovrascrive il file a ogni esecuzione
)

excel_path = r"C:\Users\<USER>\AI team projects\webservices\scripts\objects available on wordpress (2).xlsx"

objects_df = pd.read_excel(excel_path, skiprows=1)
objects_df.columns = objects_df.columns.str.strip()
print(objects_df.columns)

dict = {}
last_object_name = None
last_attribute_name = None
last_sub_attribute_name = None

def to_boolean(value):
    """Convert a cell value to boolean."""
    if pd.notna(value):
        return str(value).strip().lower() == "yes"
    return False

for _, row in objects_df.iterrows():
    if pd.notna(row["TAG"]):
        last_object_name = row["TAG"]
        if last_object_name not in dict:
            dict[last_object_name] = {
                "translatable": to_boolean(row["TRANSLATABLE (yes/no)"]),
                "self_closing": to_boolean(row["SELF-CLOSING (yes/no)"]),
                "attributes": {}
            }

    if not last_object_name or last_object_name not in dict:
        continue

    if pd.notna(row["NAME"]):
        attribute_name = row["NAME"].strip()
        if attribute_name not in dict[last_object_name]["attributes"]:
            dict[last_object_name]["attributes"][attribute_name] = {
                "translatable": to_boolean(row["TRANSLATABLE (yes/no).1"]),
                "encoding": to_boolean(row["IF TRANSLATABLE, ENCODING"]),
                "delimited_encoding": to_boolean(row["IF TRANSLATABLE, ENCODING|DELIMITED"]),
                "not_encoding": to_boolean(row["NOT ENCODING"]),
                "notes": row["NOTES"].strip() if pd.notna(row["NOTES"]) else "",
                "attributes": {}
            }
        last_attribute_name = attribute_name
        last_sub_attribute_name = None

    if pd.notna(row["Unnamed: 4"]) and last_attribute_name:
        sub_attribute_name = row["Unnamed: 4"].strip()
        if sub_attribute_name not in dict[last_object_name]["attributes"][last_attribute_name]["attributes"]:
            dict[last_object_name]["attributes"][last_attribute_name]["attributes"][sub_attribute_name] = {
                "translatable": to_boolean(row["TRANSLATABLE (yes/no).1"]),
                "encoding": to_boolean(row["IF TRANSLATABLE, ENCODING"]),
                "delimited_encoding": to_boolean(row["IF TRANSLATABLE, ENCODING|DELIMITED"]),
                "not_encoding": to_boolean(row["NOT ENCODING"]),
                "notes": row["NOTES"].strip() if pd.notna(row["NOTES"]) else "",
                "attributes": {}
            }
        last_sub_attribute_name = sub_attribute_name

    if pd.notna(row["Unnamed: 5"]) and last_sub_attribute_name:
        sub_sub_attribute_name = row["Unnamed: 5"].strip()
        dict[last_object_name]["attributes"][last_attribute_name]["attributes"][last_sub_attribute_name]["attributes"][sub_sub_attribute_name] = {
            "translatable": to_boolean(row["TRANSLATABLE (yes/no).1"]),
            "encoding": to_boolean(row["IF TRANSLATABLE, ENCODING"]),
            "delimited_encoding": to_boolean(row["IF TRANSLATABLE, ENCODING|DELIMITED"]),
            "not_encoding": to_boolean(row["NOT ENCODING"]),
            "notes": row["NOTES"].strip() if pd.notna(row["NOTES"]) else "",
        }

# Save the dictionary to a JSON file
with open("dict.json", "w", encoding="utf-8") as json_file:
    json.dump(dict, json_file, indent=4, ensure_ascii=False)