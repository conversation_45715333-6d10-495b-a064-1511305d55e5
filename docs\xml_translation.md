# XML Translation Module Documentation

This document provides detailed information about the XML translation module in the Babel Service.

## Overview

The XML translation module (`src/xml.py`) is designed to translate XML and HTML content while preserving the structure, tags, and attributes. It handles various types of attribute encodings, including JSON and pipe-delimited formats.

## Key Components

### XMLTranslator Class

The main class responsible for translating XML content.

```python
from src.xml import XMLTranslator
from utils.core import Mode

translator = XMLTranslator()
translated_texts = translator.translate(
    {"content": "<p>Hello world</p>"}, 
    "en", 
    ["fr", "es"], 
    mode=Mode.STANDARD_TURBO
)
```

#### Methods

- `translate(texts, source, targets, mode)`: Translates a dictionary of texts from the source language to multiple target languages.

### Helper Functions

- `is_attribute_to_translate(tag_name, attr_name, parent_attr, original_tag)`: Checks if an attribute should be translated.
- `is_attribute_to_decode_json(tag_name, attr_name, parent_attr, original_tag)`: Checks if an attribute is JSON-encoded.
- `is_attribute_to_decode_pipe(tag_name, attr_name, parent_attr, original_tag)`: Checks if an attribute is pipe-delimited.
- `translate_pipe_delimited_attributes(attr_value, source, targets, translator, tag_name, parent_attr, mode, original_tag)`: Translates pipe-delimited attributes.
- `extract_tag_name(tag)`: Extracts the tag name from a full tag string.

## Dictionary Configuration

The `dict.json` file controls which tags and attributes are translated. Each entry in the dictionary specifies:

1. **translatable**: Whether the tag content should be translated
2. **self_closing**: Whether the tag is self-closing
3. **attributes**: A dictionary of attributes with their translation properties:
   - **translatable**: Whether the attribute should be translated
   - **encoding**: Whether the attribute is JSON-encoded
   - **delimited_encoding**: Whether the attribute is pipe-delimited
   - **not_encoding**: Whether the attribute has no special encoding
   - **notes**: Additional notes about the attribute
   - **attributes**: Nested attributes (for complex structures)

### Example Dictionary Entry

```json
"elx_button": {
    "translatable": true,
    "self_closing": true,
    "attributes": {
        "button_text": {
            "translatable": true,
            "encoding": false,
            "delimited_encoding": false,
            "not_encoding": true,
            "notes": "",
            "attributes": {}
        },
        "button_link": {
            "translatable": false,
            "encoding": false,
            "delimited_encoding": true,
            "not_encoding": false,
            "notes": "",
            "attributes": {
                "title": {
                    "translatable": true,
                    "encoding": false,
                    "delimited_encoding": true,
                    "not_encoding": false,
                    "notes": "",
                    "attributes": {}
                }
            }
        }
    }
}
```

## Translation Process

1. **Tag Identification**: The module identifies all tags in the XML content.
2. **Placeholder Substitution**: Tags are replaced with placeholders to protect them during translation.
3. **Attribute Processing**:
   - Regular attributes are translated directly.
   - JSON-encoded attributes are decoded, translated, and re-encoded.
   - Pipe-delimited attributes are split, translated, and rejoined.
4. **Text Translation**: The text with placeholders is sent to the translation service.
5. **Reconstruction**: The translated text is reconstructed by replacing placeholders with the original (or translated) tags.

## Handling Special Cases

### JSON-Encoded Attributes

JSON-encoded attributes are identified by `"encoding": true` in the dictionary. The module:
1. Decodes the JSON string
2. Translates relevant fields based on the dictionary configuration
3. Re-encodes the JSON string

### Pipe-Delimited Attributes

Pipe-delimited attributes are identified by `"delimited_encoding": true` in the dictionary. The module:
1. Splits the string by pipe character (`|`)
2. Processes each key-value pair
3. Translates values based on the dictionary configuration
4. Rejoins the pairs with pipe characters

### Nested Attributes

The module can handle nested attributes, such as JSON attributes within pipe-delimited strings or vice versa. The nesting is specified in the dictionary configuration.

## Best Practices

1. **Dictionary Maintenance**: Keep the `dict.json` file up-to-date with all tags and attributes that need translation.
2. **Testing**: Test translations with sample XML files before using in production.
3. **Logging**: Enable detailed logging for troubleshooting by setting the appropriate log level.

## Examples

### Basic Translation

```python
from src.xml import XMLTranslator
from utils.core import Mode

# Initialize the translator
translator = XMLTranslator()

# XML content to translate
xml_content = """
<content>
    <p>Hello world</p>
    <elx_button button_text="Click me" button_link="url:https://example.com|title:Visit our website"></elx_button>
</content>
"""

# Create a dictionary with the XML content
texts_to_translate = {"xml_content": xml_content}

# Translate from English to French
translated_texts = translator.translate(
    texts_to_translate, 
    "en", 
    ["fr"], 
    mode=Mode.STANDARD_TURBO
)

# Access translated content
french_translation = translated_texts["xml_content"]["fr"]
print(french_translation)
```

### Handling Complex Attributes

For complex attributes like JSON-encoded values, ensure they are properly configured in the dictionary:

```json
"elx_multiple_columns_features": {
    "translatable": true,
    "self_closing": true,
    "attributes": {
        "values": {
            "translatable": true,
            "encoding": true,
            "delimited_encoding": false,
            "not_encoding": false,
            "notes": "",
            "attributes": {
                "title": {
                    "translatable": true,
                    "encoding": true,
                    "delimited_encoding": false,
                    "not_encoding": false,
                    "notes": "",
                    "attributes": {}
                },
                "text": {
                    "translatable": true,
                    "encoding": true,
                    "delimited_encoding": false,
                    "not_encoding": false,
                    "notes": "",
                    "attributes": {}
                }
            }
        }
    }
}
```

## Troubleshooting

### Common Issues

1. **Untranslated Content**: Check if the tag and attribute are properly configured in the dictionary.
2. **Missing Tags**: Ensure the XML is well-formed and all tags are properly closed.
3. **JSON Parsing Errors**: Verify that JSON-encoded attributes contain valid JSON.
4. **Pipe-Delimited Parsing Errors**: Check that pipe-delimited strings follow the expected format (e.g., `key:value|key:value`).

### Logging

Enable detailed logging to troubleshoot issues:

```python
import logging
from utils.core import get_logger

logger = get_logger(__file__)
logger.setLevel(logging.DEBUG)
```

## Performance Considerations

- The module extracts tag names once and reuses them to improve performance.
- For large XML files, consider processing them in chunks.
- JSON parsing and encoding can be resource-intensive for large JSON structures.
