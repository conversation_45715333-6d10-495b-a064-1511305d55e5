# Getting Started with Babel Service

This guide will help you quickly get started with the Babel Service for translating XML/HTML content.

## Prerequisites

- Python 3.8 or higher
- pip and pip-tools
- Azure Translator API key (or other supported translation service)

## Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd babel-service
   ```

2. **Set up environment with provided PowerShell script**

   ```PowerShell
   ./setup.ps1
   ```

4. **Configure environment variables**

   Create a `.env` file in the root directory with your Azure Translator API credentials:

   ```
   TRANSLATOR_KEY=your_translator_key
   TRANSLATOR_REGION=your_region
   ```

## Quick Start

### Running the Built-in Test

The module includes a built-in test that you can run:

```bash
python -m src.xml
```

This will translate a test XML file and save the output to the test directory.

## Understanding the Dictionary

The `dict.json` file controls which tags and attributes are translated. Here's a simple example:

```json
"p": {
    "translatable": true,
    "self_closing": false,
    "attributes": {
        "title": {
            "translatable": true,
            "encoding": false,
            "delimited_encoding": false,
            "not_encoding": true,
            "notes": "",
            "attributes": {}
        }
    }
}
```

This configuration:
- Makes the content of `<p>` tags translatable
- Specifies that `<p>` tags are not self-closing
- Makes the `title` attribute of `<p>` tags translatable
- Specifies that the `title` attribute has no special encoding

## Translation Modes

The service supports different translation modes:

- `Mode.STANDARD_TURBO`: Fast translation with good quality
- `Mode.STANDARD_PROOFREAD`: Higher quality translation with proofreading (individual processing)
- `Mode.STANDARD_PROOFREAD_BATCH`: Higher quality translation with batch proofreading (more efficient for multiple texts)
- `Mode.STANDARD_CREATIVE`: Creative translation for marketing content

Example:

```python
# For high-quality translation (individual processing)
translated_texts = translator.translate(
    texts_to_translate,
    "en",
    ["de"],
    mode=Mode.STANDARD_PROOFREAD
)

# For high-quality translation with batch processing (more efficient for multiple texts)
translated_texts = translator.translate(
    texts_to_translate,
    "en",
    ["de"],
    mode=Mode.STANDARD_PROOFREAD_BATCH
)
```

## Handling Special Attributes

### JSON-Encoded Attributes

For attributes that contain JSON data:

```python
# Example XML with JSON-encoded attribute
xml_content = '<elx_multiple_columns_features values="[{\"title\":\"Feature 1\",\"text\":\"Description\"}]"></elx_multiple_columns_features>'

# Make sure the dictionary has the correct configuration
# "values": {
#     "translatable": true,
#     "encoding": true,  # This indicates JSON encoding
#     "delimited_encoding": false,
#     "not_encoding": false,
#     ...
# }
```

### Pipe-Delimited Attributes

For attributes that use pipe-delimited format:

```python
# Example XML with pipe-delimited attribute
xml_content = '<elx_button button_link="url:https://example.com|title:Visit our website"></elx_button>'

# Make sure the dictionary has the correct configuration
# "button_link": {
#     "translatable": false,
#     "encoding": false,
#     "delimited_encoding": true,  # This indicates pipe-delimited format
#     "not_encoding": false,
#     ...
# }
```

## Next Steps

- Read the [XML Translation Module Documentation](xml_translation.md) for more detailed information
- Explore the [README.md](../README.md) for a complete overview of the project
