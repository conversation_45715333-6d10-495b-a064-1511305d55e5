# Batch Proofreading Feature

## Overview

The batch proofreading feature provides an efficient way to proofread multiple translations in a single API call, reducing latency and improving throughput compared to individual proofreading requests.

## New Translation Mode

### `Mode.STANDARD_PROOFREAD_BATCH`

This new mode processes multiple texts in a single batch request to the proofreading service, offering:

- **Improved Efficiency**: Single API call for multiple texts
- **Reduced Latency**: Lower overall processing time for multiple translations
- **Cost Optimization**: Fewer API calls to the underlying LLM service
- **Fallback Safety**: Automatically falls back to individual processing if batch fails

## Implementation Details

### ProofreaderClient Enhancements

The `ProofreaderClient` class now includes:

#### `review_translation_batch(source_texts, target_texts)`

- **Input**: Lists of `LocalizedText` objects for source and target texts
- **Output**: List of reviewed `LocalizedText` objects
- **Features**:
  - Handles empty texts gracefully
  - Maintains order of input texts
  - Provides structured batch request formatting
  - Robust response parsing with fallback handling

#### Key Methods

1. **`_build_batch_request(text_pairs)`**: Formats multiple translation pairs into a single structured request
2. **`_parse_batch_response(response_content, text_pairs)`**: Extracts individual reviewed translations from the batch response

### TextTranslator Integration

The `TextTranslator` class has been updated to support the new batch mode:

- Automatic detection of `Mode.STANDARD_PROOFREAD_BATCH`
- Fallback to individual processing if batch fails
- Same word protection and post-processing as standard proofread mode

## Usage Examples

### Basic Usage

```python
from src.textual import TextTranslator
from utils.core import Mode

translator = TextTranslator()

texts_to_translate = {
    "text1": "Hello world",
    "text2": "This is a test",
    "text3": "Electrolux Professional equipment"
}

# Use batch proofreading mode
result = translator.translate(
    texts_to_translate,
    source="en",
    target=["de"],
    mode=Mode.STANDARD_PROOFREAD_BATCH
)
```

### Direct Client Usage

```python
from utils.clients import ProofreaderClient
from utils.core import Language, LocalizedText

client = ProofreaderClient()

# Prepare source and target texts
source_texts = [
    LocalizedText("Hello world", en_lang),
    LocalizedText("This is a test", en_lang),
]

target_texts = [
    LocalizedText("Hallo Welt", de_lang),
    LocalizedText("Das ist ein Test", de_lang),
]

# Batch review
reviewed_texts = client.review_translation_batch(source_texts, target_texts)
```

## Performance Considerations

### When to Use Batch Mode

- **Multiple Texts**: When processing 2 or more texts simultaneously
- **Similar Content**: Works best with texts of similar length and complexity
- **High Throughput**: When processing large volumes of content

### When to Use Individual Mode

- **Single Text**: For individual translation requests
- **Mixed Content**: When texts vary significantly in length or complexity
- **Real-time Processing**: When immediate response for each text is needed

## Error Handling

The batch proofreading implementation includes robust error handling:

1. **Batch Failure Fallback**: Automatically switches to individual processing if batch fails
2. **Empty Text Handling**: Gracefully processes empty or whitespace-only texts
3. **Response Parsing**: Multiple parsing strategies with fallback to original translations
4. **Logging**: Comprehensive logging for debugging and monitoring

## Testing

A comprehensive test suite is provided in `test_batch_proofread.py`:

```bash
python test_batch_proofread.py
```

The test suite covers:
- Direct client batch functionality
- TextTranslator integration
- Empty text handling
- Error scenarios

## Configuration

No additional configuration is required. The batch proofreading feature uses the same configuration as the standard proofreading mode:

- Same Azure OpenAI endpoint and credentials
- Same system prompts and temperature settings
- Same retry logic and error handling

## Monitoring and Logging

The feature includes detailed logging:

- Batch processing start/completion
- Fallback activation
- Individual text processing within batches
- Error conditions and recovery

Log messages use the standard logging framework and appear in the application logs with appropriate log levels.

## Migration Guide

### From Individual to Batch Mode

Simply change the mode parameter:

```python
# Before (individual)
result = translator.translate(texts, "en", ["de"], mode=Mode.STANDARD_PROOFREAD)

# After (batch)
result = translator.translate(texts, "en", ["de"], mode=Mode.STANDARD_PROOFREAD_BATCH)
```

### Backward Compatibility

- All existing functionality remains unchanged
- `Mode.STANDARD_PROOFREAD` continues to work as before
- No breaking changes to existing APIs

## Future Enhancements

Potential future improvements:

1. **Adaptive Batching**: Automatically choose batch size based on content
2. **Parallel Batch Processing**: Process multiple batches concurrently
3. **Batch Size Configuration**: Configurable maximum batch sizes
4. **Performance Metrics**: Detailed performance monitoring and reporting
