import json
from openpyxl import load_workbook
import os

def convert_excel_to_json(excel_file_path, output_json_path):
    """
    Convert the Excel file back to JSON format, reconstructing the original structure
    """
    
    # Load the Excel file
    wb = load_workbook(excel_file_path)
    ws = wb.active
    
    # Get headers
    headers = []
    for col in range(1, ws.max_column + 1):
        cell_value = ws.cell(row=1, column=col).value
        headers.append(cell_value if cell_value else f"Col_{col}")
    
    # Reconstruct JSON structure
    result_json = {}
    current_tag = None
    current_path = [None, None, None, None]  # Track current path for levels 0-3
    
    # Process each row
    for row_num in range(2, ws.max_row + 1):
        # Read row data
        row_data = {}
        for col in range(1, ws.max_column + 1):
            cell_value = ws.cell(row=row_num, column=col).value
            row_data[headers[col-1]] = cell_value if cell_value else ''
        
        tag_name = row_data['TAG']
        
        # If we have a new tag (not empty)
        if tag_name and tag_name.strip():
            current_tag = tag_name.strip()
            current_path = [None, None, None, None]  # Reset path for new tag
            
            # Initialize tag structure
            result_json[current_tag] = {
                'translatable': row_data['TAG TRANSLATABLE'].lower() == 'yes' if row_data['TAG TRANSLATABLE'] else False,
                'self_closing': row_data['TAG SELF-CLOSING'].lower() == 'yes' if row_data['TAG SELF-CLOSING'] else False,
                'attributes': {}
            }
        
        # Process attributes from level columns
        if current_tag:
            # Check each level and update the current path
            for level in range(4):  # Levels 0-3
                level_col = f'ATTRIBUTE LEVEL {level}'
                attr_name = row_data[level_col]
                if attr_name and attr_name.strip():
                    # Update this level and clear all deeper levels
                    current_path[level] = attr_name.strip()
                    for deeper_level in range(level + 1, 4):
                        current_path[deeper_level] = None
                    break  # Stop processing levels after finding the first non-empty one
            
            # Create the attribute path (remove None values)
            attribute_path = [attr for attr in current_path if attr is not None]
            
            # If we have an attribute path, create the nested structure
            if attribute_path:
                create_attribute_at_path(result_json[current_tag]['attributes'], 
                                       attribute_path, row_data)
    
    # Fix intermediate attributes with correct properties
    fix_intermediate_attributes(result_json, excel_file_path)
    
    # Save to JSON
    with open(output_json_path, 'w', encoding='utf-8') as f:
        json.dump(result_json, f, indent=4, ensure_ascii=False)
    
    return result_json

def create_attribute_at_path(root_attributes, path, row_data):
    """
    Create an attribute at the specified path, creating intermediate containers as needed
    """
    current_dict = root_attributes
    
    # Navigate to the parent location, creating intermediate attributes as needed
    for i, attr_name in enumerate(path[:-1]):
        if attr_name not in current_dict:
            # Create intermediate attribute with default values
            current_dict[attr_name] = {
                'translatable': True,  # Will be fixed in second pass
                'encoding': True,      # Will be fixed in second pass
                'delimited_encoding': False,
                'not_encoding': False,
                'notes': '',
                'attributes': {}
            }
        
        # Ensure attributes dict exists
        if 'attributes' not in current_dict[attr_name]:
            current_dict[attr_name]['attributes'] = {}
            
        current_dict = current_dict[attr_name]['attributes']
    
    # Create the final attribute
    final_attr_name = path[-1]
    current_dict[final_attr_name] = {
        'translatable': row_data['ATTR TRANSLATABLE'].lower() == 'yes' if row_data['ATTR TRANSLATABLE'] else False,
        'encoding': row_data['ATTR ENCODING'].lower() == 'yes' if row_data['ATTR ENCODING'] else False,
        'delimited_encoding': row_data['ATTR DELIMITED_ENCODING'].lower() == 'yes' if row_data['ATTR DELIMITED_ENCODING'] else False,
        'not_encoding': row_data['ATTR NOT_ENCODING'].lower() == 'yes' if row_data['ATTR NOT_ENCODING'] else False,
        'notes': row_data['ATTR NOTES'] if row_data['ATTR NOTES'] else '',
        'attributes': {}
    }

def fix_intermediate_attributes(result_json, excel_file_path):
    """
    Second pass to fix the properties of intermediate attributes by finding their definition rows
    """
    wb = load_workbook(excel_file_path)
    ws = wb.active
    
    # Get headers
    headers = []
    for col in range(1, ws.max_column + 1):
        cell_value = ws.cell(row=1, column=col).value
        headers.append(cell_value if cell_value else f"Col_{col}")
    
    # Build a map of attribute definitions
    attribute_definitions = {}
    current_tag = None
    
    for row_num in range(2, ws.max_row + 1):
        # Read row data
        row_data = {}
        for col in range(1, ws.max_column + 1):
            cell_value = ws.cell(row=row_num, column=col).value
            row_data[headers[col-1]] = cell_value if cell_value else ''
        
        tag_name = row_data['TAG']
        if tag_name and tag_name.strip():
            current_tag = tag_name.strip()
            attribute_definitions[current_tag] = {}
        
        if current_tag:
            # Find which levels have values to build exact path
            attribute_path = []
            for level in range(4):  # Levels 0-3
                level_col = f'ATTRIBUTE LEVEL {level}'
                attr_name = row_data[level_col]
                if attr_name and attr_name.strip():
                    attribute_path.append(attr_name.strip())
                else:
                    break  # Stop at first empty level
            
            if attribute_path:
                path_key = '.'.join(attribute_path)
                attribute_definitions[current_tag][path_key] = {
                    'translatable': row_data['ATTR TRANSLATABLE'].lower() == 'yes' if row_data['ATTR TRANSLATABLE'] else False,
                    'encoding': row_data['ATTR ENCODING'].lower() == 'yes' if row_data['ATTR ENCODING'] else False,
                    'delimited_encoding': row_data['ATTR DELIMITED_ENCODING'].lower() == 'yes' if row_data['ATTR DELIMITED_ENCODING'] else False,
                    'not_encoding': row_data['ATTR NOT_ENCODING'].lower() == 'yes' if row_data['ATTR NOT_ENCODING'] else False,
                    'notes': row_data['ATTR NOTES'] if row_data['ATTR NOTES'] else ''
                }
    
    # Now fix the intermediate attributes
    for tag_name, tag_data in result_json.items():
        if tag_name in attribute_definitions:
            fix_attributes_in_dict(tag_data['attributes'], attribute_definitions[tag_name], [])

def fix_attributes_in_dict(attributes_dict, definitions, current_path):
    """
    Recursively fix attribute properties using the definitions
    """
    for attr_name, attr_data in attributes_dict.items():
        path = current_path + [attr_name]
        path_key = '.'.join(path)
        
        if path_key in definitions:
            # Update the properties with the correct values
            definition = definitions[path_key]
            attr_data['translatable'] = definition['translatable']
            attr_data['encoding'] = definition['encoding']
            attr_data['delimited_encoding'] = definition['delimited_encoding']
            attr_data['not_encoding'] = definition['not_encoding']
            attr_data['notes'] = definition['notes']
        
        # Recursively fix nested attributes
        if 'attributes' in attr_data and attr_data['attributes']:
            fix_attributes_in_dict(attr_data['attributes'], definitions, path)

def compare_with_original(original_file, reconstructed_data):
    """
    Compare the reconstructed JSON with the original to check accuracy
    """
    try:
        with open(original_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        print("🔍 Comparison with original JSON:")
        
        # Compare number of tags
        orig_tags = len(original_data)
        recon_tags = len(reconstructed_data)
        print(f"   Tags: Original={orig_tags}, Reconstructed={recon_tags}")
        
        # Check for missing or extra tags
        orig_tag_names = set(original_data.keys())
        recon_tag_names = set(reconstructed_data.keys())
        
        missing_tags = orig_tag_names - recon_tag_names
        extra_tags = recon_tag_names - orig_tag_names
        
        if missing_tags:
            print(f"   ❌ Missing tags: {list(missing_tags)[:5]}{'...' if len(missing_tags) > 5 else ''}")
        if extra_tags:
            print(f"   ❌ Extra tags: {list(extra_tags)[:5]}{'...' if len(extra_tags) > 5 else ''}")
        
        if not missing_tags and not extra_tags:
            print("   ✅ All tags preserved")
        
        # Detailed comparison for problematic tags
        problematic_tags = ['elx_multiple_columns', 'elx_promo_top_banner']
        for tag in problematic_tags:
            if tag in original_data and tag in reconstructed_data:
                orig_attrs = count_nested_attributes(original_data[tag].get('attributes', {}))
                recon_attrs = count_nested_attributes(reconstructed_data[tag].get('attributes', {}))
                status = "✅" if orig_attrs == recon_attrs else "❌"
                print(f"   {status} {tag}: Original={orig_attrs} attrs, Reconstructed={recon_attrs} attrs")
        
    except Exception as e:
        print(f"   ⚠️  Could not compare with original: {e}")

def main():
    # File paths
    excel_file = 'tags_attributes.xlsx'
    output_json = 'reconstructed_dict.json'
    original_json = 'src/dict.json'
    
    # Check if input file exists
    if not os.path.exists(excel_file):
        print(f"❌ Error: {excel_file} not found!")
        print("Please run the json_to_excel_converter.py script first to create the Excel file.")
        return
    
    try:
        print("🔄 Converting Excel back to JSON...")
        reconstructed_data = convert_excel_to_json(excel_file, output_json)
        
        print(f"✅ Conversion completed successfully!")
        print(f"📁 Output file: {output_json}")
        print(f"📊 Total tags: {len(reconstructed_data)}")
        
        # Show some statistics
        total_attributes = 0
        tags_with_attrs = 0
        
        for tag_name, tag_data in reconstructed_data.items():
            attrs = tag_data.get('attributes', {})
            if attrs:
                tags_with_attrs += 1
                total_attributes += count_nested_attributes(attrs)
        
        print(f"\n📈 Statistics:")
        print(f"   - Tags with attributes: {tags_with_attrs}")
        print(f"   - Total attributes: {total_attributes}")
        
        # Compare with original if available
        if os.path.exists(original_json):
            compare_with_original(original_json, reconstructed_data)
        
        print(f"\n📋 Sample reconstructed tags:")
        for i, (tag_name, tag_data) in enumerate(list(reconstructed_data.items())[:5]):
            attr_count = count_nested_attributes(tag_data.get('attributes', {}))
            translatable = "✓" if tag_data.get('translatable') else "✗"
            self_closing = "✓" if tag_data.get('self_closing') else "✗"
            print(f"   {i+1}. {tag_name}: {attr_count} attrs, translatable={translatable}, self_closing={self_closing}")
            
    except Exception as e:
        print(f"❌ Error during conversion: {str(e)}")
        import traceback
        traceback.print_exc()

def count_nested_attributes(attributes_dict):
    """
    Recursively count attributes including nested ones
    """
    count = len(attributes_dict)
    for attr_data in attributes_dict.values():
        if isinstance(attr_data, dict) and 'attributes' in attr_data:
            count += count_nested_attributes(attr_data['attributes'])
    return count

if __name__ == "__main__":
    main()
