Function fillWebConfig($execution_env) {
    # Load the XML file
    $xmlDoc = New-Object System.Xml.XmlDocument

    $xmlDoc.Load("$PSScriptRoot\web.config.example")

    # Select the appSettings node
    $appSettingsNode = $xmlDoc.SelectSingleNode("//appSettings")

    # Remove all child nodes of appSettings
    $appSettingsNode.RemoveAll()

    foreach ($line in Get-Content ($execution_env + "/.env")) {
        $splitted = $line.split('=').trim()
        if ($splitted.Count -gt 1 ) {
            $newElement1 = $xmlDoc.CreateElement("add")
            $newElement1.SetAttribute("key", $splitted[0])
            $newElement1.SetAttribute("value", $splitted[1])
            $appSettingsNode.AppendChild($newElement1)
        }
    }

    # Save the modified XML document
    $xmlDoc.Save("$PSScriptRoot\web.config")

    Write-Host "Successfully updated web.config"

    # Ensure wfastcgi is installed on server
    Invoke-Expression 'pip install wfastcgi'
}


# check if virtual environment already exists
$venv_exists = $false
if (Test-Path -Path 'venv') {
    $venv_exists = $true
} else {
    Write-Output "Setup new virtual environment..."
    Invoke-Expression 'python -m venv venv'
}

# Activate virtual environment
Write-Output "Activate virtual environment..."
Invoke-Expression '& .\venv\Scripts\Activate.ps1'

# install requirements if needed
if (!$venv_exists) {
    Write-Output "Installing requirements..."
    Invoke-Expression 'pip install -r requirements.txt'
} else {
    Write-Output "Updating existing virtual environment..."
    Invoke-Expression 'pip install -r requirements.txt'
}

$execution_env = $env:FLASK_ENV
switch ($execution_env) {
    "PRD" { fillWebConfig 'PRD' }
    "STG" { fillWebConfig 'STG' }
    "DEV"  { fillWebConfig 'DEV' }
    default { # local development server$
        foreach ($line in Get-Content ".env") {
            if ($line) {
                $name, $value = $line.split('=').trim()
                [Environment]::SetEnvironmentVariable($name, $value, "Process")
            }
        }
        Write-Host "Successfully updated environment variables"
    }
}
