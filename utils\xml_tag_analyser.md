# XML Tag Analyzer Documentation

A Python script for analyzing XML tags and shortcodes (square brackets tags) in files, detecting unclosed tags, validating structure, and providing detailed reports.

## Features

- **XML Tag Analysis**: Detects opening, closing, and self-closing XML tags
- **Shortcode Analysis**: Analyzes bracket-style shortcodes `[tag]` and `[/tag]`
- **Structure Validation**: Identifies unclosed tags and mismatched pairs
- **Smart Detection**: Automatically determines which shortcodes are self-closing based on usage patterns
- **Descriptor Support**: Validates shortcodes against predefined descriptors
- **Flexible Output**: Console display with optional file saving

## Basic Usage

### Simple Analysis
```bash
python xml_tag_analyser.py myfile.xml
```

### With Output File
```bash
python xml_tag_analyser.py myfile.xml -o analysis_results.txt
```

### Auto-save Results
```bash
python xml_tag_analyser.py myfile.xml --auto-save
```

## Advanced Usage

### Using Shortcode TAG Descriptors
Create a JSON file with shortcode definitions:

```json
{
    "elx_hero_title label": {
        "translatable": true,
        "self_closing": false,
        "attributes": {
            "label": {
                "translatable": true,
                "encoding": false,
                "delimited_encoding": false,
                "not_encoding": true,
                "notes": "",
                "attributes": {}
            },
            "video_link": {
                "translatable": false,
                "encoding": false,
                "delimited_encoding": false,
                "not_encoding": false,
                "notes": "",
                "attributes": {}
            }
        }
    },
    "elx_simple_link_block": {
        "translatable": true,
        "self_closing": false,
        "attributes": {
            "link": {
                "translatable": false,
                "encoding": false,
                "delimited_encoding": false,
                "not_encoding": false,
                "notes": "",
                "attributes": {
                    "url": {
                        "translatable": false,
                        "encoding": false,
                        "delimited_encoding": false,
                        "not_encoding": false,
                        "notes": "",
                        "attributes": {}
                    },
                    "title": {
                        "translatable": true,
                        "encoding": false,
                        "delimited_encoding": true,
                        "not_encoding": false,
                        "notes": "",
                        "attributes": {}
                    }
                }
            }
        }
    },
}
```

Then run:
```bash
python xml_tag_analyser.py myfile.txt --descriptors shortcode_definitions.json
```

### Different File Encoding
```bash
python xml_tag_analyser.py myfile.xml --encoding iso-8859-1
```

## Command Line Options

| Option | Description |
|--------|-------------|
| `input_file` | **Required.** File to analyze |
| `-o, --output FILE` | Save results to specified file |
| `--auto-save` | Automatically save results without prompting |
| `--encoding ENCODING` | File encoding (default: utf-8) |
| `--descriptors FILE` | JSON file with shortcode definitions |

## Output Sections

### Summary
- Total tag counts (XML and shortcodes)
- Unique tag types
- Unclosed tag counts

### XML Tags Found
- Tag names with occurrence counts
- Breakdown by type: opening, closing, self-closing

### Shortcodes Found
- Shortcode names with occurrence counts
- Auto-detected self-closing behavior

### Problems Detected
- Unclosed XML tags with positions
- Unclosed shortcodes with positions
- Unknown shortcodes (not in descriptors)
- Self-closing mismatches vs. descriptors
