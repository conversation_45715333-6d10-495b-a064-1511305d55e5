import json
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment, NamedStyle
from openpyxl.utils import get_column_letter
import os

def extract_attributes_as_rows(attributes, parent_path="", level=0):
    """
    Recursively extract attributes and return them as individual rows with hierarchy
    """
    attribute_rows = []
    
    for attr_name, attr_data in attributes.items():
        current_path = f"{parent_path}.{attr_name}" if parent_path else attr_name
        
        if isinstance(attr_data, dict):
            # Create a row for this attribute
            row = {
                'attribute_name': attr_name,
                'attribute_path': current_path,
                'level': level,
                'parent_path': parent_path if parent_path else '',
                'notes': attr_data.get('notes', ''),
                'translatable': attr_data.get('translatable', ''),
                'encoding': attr_data.get('encoding', ''),
                'delimited_encoding': attr_data.get('delimited_encoding', ''),
                'not_encoding': attr_data.get('not_encoding', ''),
                'has_nested': bool(attr_data.get('attributes', {}))
            }
            attribute_rows.append(row)
            
            # Recursively process nested attributes
            if 'attributes' in attr_data and attr_data['attributes']:
                nested_rows = extract_attributes_as_rows(attr_data['attributes'], current_path, level + 1)
                attribute_rows.extend(nested_rows)
    
    return attribute_rows

def convert_dict_to_excel(json_file_path, output_file_path):
    """
    Convert the dict.json file to Excel format following the WordPress objects structure
    """
    
    # Load the JSON data
    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Create Excel workbook with formatting
    wb = Workbook()
    ws = wb.active
    ws.title = "Tags_and_Attributes"
    
    # Define styles
    header_style = NamedStyle(name="header_style")
    header_style.font = Font(name="Calibri", size=11, bold=True, color="FFFFFF")
    header_style.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_style.border = Border(
        left=Side(style='thin', color='000000'),
        right=Side(style='thin', color='000000'),
        top=Side(style='thin', color='000000'),
        bottom=Side(style='thin', color='000000')
    )
    header_style.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    tag_style = NamedStyle(name="tag_style")
    tag_style.font = Font(name="Calibri", size=10, bold=True)
    tag_style.fill = PatternFill(start_color="E7F3FF", end_color="E7F3FF", fill_type="solid")
    tag_style.alignment = Alignment(horizontal='left', vertical='center')
    
    attr_style = NamedStyle(name="attr_style")
    attr_style.font = Font(name="Calibri", size=10)
    attr_style.alignment = Alignment(horizontal='left', vertical='center')
    
    # Register styles
    if 'header_style' not in wb.named_styles:
        wb.add_named_style(header_style)
    if 'tag_style' not in wb.named_styles:
        wb.add_named_style(tag_style)
    if 'attr_style' not in wb.named_styles:
        wb.add_named_style(attr_style)
    
    # Actually, let's use clearer headers based on the pattern with columns for nested levels
    headers = [
        'TAG', 'TAG TRANSLATABLE', 'TAG SELF-CLOSING', 'ATTRIBUTE LEVEL 0',
        'ATTRIBUTE LEVEL 1', 'ATTRIBUTE LEVEL 2', 'ATTRIBUTE LEVEL 3', 'ATTR TRANSLATABLE',
        'ATTR ENCODING', 'ATTR DELIMITED_ENCODING', 
        'ATTR NOT_ENCODING', 'ATTR NOTES'
    ]
    
    # Add headers with enhanced styling
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.style = header_style
        
        # Set specific column widths based on content
        if col <= 3:  # Tag columns
            ws.column_dimensions[get_column_letter(col)].width = 20
        elif col <= 7:  # Attribute level columns
            ws.column_dimensions[get_column_letter(col)].width = 25
        else:  # Property columns
            ws.column_dimensions[get_column_letter(col)].width = 15
    
    # Process data and add rows
    current_row = 2
    tag_start_rows = []  # Track where each tag starts for border separation
    
    for tag_name, tag_data in data.items():
        tag_start_rows.append(current_row)  # Record the start of this tag
        
        # Get all attribute rows for this tag
        attribute_rows = []
        if 'attributes' in tag_data and tag_data['attributes']:
            attribute_rows = extract_attributes_as_rows(tag_data['attributes'])
        
        if attribute_rows:
            # First row: Tag information + first attribute
            first_attr = attribute_rows[0]
            
            # Tag columns with enhanced styling
            tag_cell = ws.cell(row=current_row, column=1, value=tag_name)
            tag_cell.style = tag_style
            
            translatable_cell = ws.cell(row=current_row, column=2, value='yes' if tag_data.get('translatable') else 'no')
            translatable_cell.style = attr_style
            # Remove background color formatting
                
            self_closing_cell = ws.cell(row=current_row, column=3, value='yes' if tag_data.get('self_closing') else 'no')
            self_closing_cell.style = attr_style
            # Remove background color formatting
            
            # First attribute in appropriate level column
            attr_column = 4 + first_attr['level']  # Column 4 = level 0, column 5 = level 1, etc.
            attr_cell = ws.cell(row=current_row, column=attr_column, value=first_attr['attribute_name'])
            attr_cell.style = attr_style
            
            # Apply level-specific font styling
            if first_attr['level'] == 0:
                attr_cell.font = Font(name="Calibri", size=10, italic=True)  # Cursive for level 0
            else:
                attr_cell.font = Font(name="Calibri", size=10, bold=True)
            
            # Apply level-specific background color
            level_colors = ["E6F3FF", "CCE7FF", "B3DBFF", "99CCFF", "80BFFF"]  # Colors from light to dark blue
            level_color = level_colors[min(first_attr['level'], len(level_colors)-1)]
            attr_cell.fill = PatternFill(start_color=level_color, end_color=level_color, fill_type="solid")
            
            # Clear other attribute level columns for this row
            for col in range(4, 8):  # Columns 4-7 are for attribute levels 0-3
                if col != attr_column:
                    cell = ws.cell(row=current_row, column=col, value='')
                    cell.style = attr_style
            
            # Attribute properties with conditional formatting
            attr_props = [
                (8, first_attr['translatable']),
                (9, first_attr['encoding']),
                (10, first_attr['delimited_encoding']),
                (11, first_attr['not_encoding'])
            ]
            
            for col_idx, prop_value in attr_props:
                cell = ws.cell(row=current_row, column=col_idx, value='yes' if prop_value else 'no')
                cell.style = attr_style
                # Remove background color formatting
            
            # Notes column
            notes_cell = ws.cell(row=current_row, column=12, value=first_attr['notes'])
            notes_cell.style = attr_style
            # Remove background color formatting
            
            current_row += 1
            
            # Subsequent rows: Only attributes (tag columns empty like in reference)
            for attr_row in attribute_rows[1:]:
                # Tag columns - empty but styled
                for col in range(1, 4):
                    cell = ws.cell(row=current_row, column=col, value='')
                    cell.style = attr_style
                
                # Place attribute in the appropriate level column
                attr_column = 4 + attr_row['level']  # Column 4 = level 0, column 5 = level 1, etc.
                attr_cell = ws.cell(row=current_row, column=attr_column, value=attr_row['attribute_name'])
                attr_cell.style = attr_style
                
                # Apply level-specific styling
                level_colors = ["E6F3FF", "E6F3FF", "E6F3FF", "CCE7FF", "B3DBFF"]  # Same color for levels 0, 1, 2
                level_color = level_colors[min(attr_row['level'], len(level_colors)-1)]
                attr_cell.fill = PatternFill(start_color=level_color, end_color=level_color, fill_type="solid")
                
                # Apply level-specific font styling
                if attr_row['level'] == 0:
                    attr_cell.font = Font(name="Calibri", size=10, italic=True)  # Cursive for level 0
                elif attr_row['level'] > 0:
                    attr_cell.font = Font(name="Calibri", size=10, italic=True)
                
                # Clear other attribute level columns for this row
                for col in range(4, 8):  # Columns 4-7 are for attribute levels 0-3
                    if col != attr_column:
                        cell = ws.cell(row=current_row, column=col, value='')
                        cell.style = attr_style
                
                # Attribute properties with conditional formatting
                attr_props = [
                    (8, attr_row['translatable']),
                    (9, attr_row['encoding']),
                    (10, attr_row['delimited_encoding']),
                    (11, attr_row['not_encoding'])
                ]
                
                for col_idx, prop_value in attr_props:
                    cell = ws.cell(row=current_row, column=col_idx, value='yes' if prop_value else 'no')
                    cell.style = attr_style
                    # Remove background color formatting
                
                # Notes column
                notes_cell = ws.cell(row=current_row, column=12, value=attr_row['notes'])
                notes_cell.style = attr_style
                # Remove background color formatting from notes column
                
                current_row += 1
        else:
            # If tag has no attributes, create a single row like in reference
            tag_cell = ws.cell(row=current_row, column=1, value=tag_name)
            tag_cell.style = tag_style
            
            translatable_cell = ws.cell(row=current_row, column=2, value='yes' if tag_data.get('translatable') else 'no')
            translatable_cell.style = attr_style
            # Remove background color formatting
                
            self_closing_cell = ws.cell(row=current_row, column=3, value='yes' if tag_data.get('self_closing') else 'no')
            self_closing_cell.style = attr_style
            # Remove background color formatting
            
            # Empty attribute columns
            for col in range(4, 13):
                cell = ws.cell(row=current_row, column=col, value='')
                cell.style = attr_style
            
            current_row += 1
    
    # Add borders to all cells
    medium_border = Border(
        left=Side(style='medium', color='366092'),
        right=Side(style='medium', color='366092'),
        top=Side(style='medium', color='366092'),
        bottom=Side(style='medium', color='366092')
    )
    
    thin_border = Border(
        left=Side(style='thin', color='B0B0B0'),
        right=Side(style='thin', color='B0B0B0'),
        top=Side(style='thin', color='B0B0B0'),
        bottom=Side(style='thin', color='B0B0B0')
    )
    
    # Border for tag separation
    tag_separator_border = Border(
        left=Side(style='thin', color='B0B0B0'),
        right=Side(style='thin', color='B0B0B0'),
        top=Side(style='medium', color='366092'),  # Thicker top border for tag separation
        bottom=Side(style='thin', color='B0B0B0')
    )
    
    # Apply borders
    for row in range(1, current_row):
        for col in range(1, 13):
            cell = ws.cell(row=row, column=col)
            if row == 1:  # Header row
                cell.border = medium_border
            else:
                cell.border = thin_border
            
            # Ensure all cells have proper alignment
            if not hasattr(cell, 'alignment') or cell.alignment is None:
                cell.alignment = Alignment(vertical='center', wrap_text=True)
    
    # Freeze the header row
    ws.freeze_panes = 'A2'
    
    # Save the workbook
    wb.save(output_file_path)
    
    # Create a summary DataFrame for statistics
    rows_data = []
    for tag_name, tag_data in data.items():
        if 'attributes' in tag_data and tag_data['attributes']:
            attribute_rows = extract_attributes_as_rows(tag_data['attributes'])
            for attr_row in attribute_rows:
                rows_data.append({
                    'tag_name': tag_name,
                    'attribute_name': attr_row['attribute_name'],
                    'level': attr_row['level']
                })
        else:
            rows_data.append({
                'tag_name': tag_name,
                'attribute_name': '',
                'level': 0
            })
    
    return pd.DataFrame(rows_data)

def main():
    # File paths
    json_file = 'src/dict.json'
    output_file = 'tags_attributes.xlsx'
    
    # Check if input file exists
    if not os.path.exists(json_file):
        print(f"Error: {json_file} not found!")
        return
    
    try:
        # Convert to Excel
        df = convert_dict_to_excel(json_file, output_file)
        
        print(f"Conversion completed successfully!")
        print(f"Output file: {output_file}")
        print(f"Total rows: {len(df)}")
        
        # Show some statistics
        unique_tags = df['tag_name'].nunique()
        total_attributes = df[df['attribute_name'] != ''].shape[0]
        level_counts = df[df['attribute_name'] != '']['level'].value_counts().sort_index()

        
        print("\nAttribute hierarchy levels:")
        for level, count in level_counts.items():
            indent = "  " * level
            print(f"   - Level {level}{indent}: {count} attributes")
        print()
            
    except Exception as e:
        print(f"❌ Error during conversion: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()