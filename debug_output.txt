=== Original XML ===
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE document>
<?PubTbl cell border-left-width="0.50pt" border-right-width="0.50pt" border-top-width="0.50pt"?>
<document>
    <title>Test Document</title>
    <p>Some text content</p>
</document>

=== Preamble (what gets preserved) ===
'<?xml version="1.0" encoding="UTF-8"?>\n<!DOCTYPE document>\n<?PubTbl cell border-left-width="0.50pt" border-right-width="0.50pt" border-top-width="0.50pt"?>\n'

Preamble content:
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE document>
<?PubTbl cell border-left-width="0.50pt" border-right-width="0.50pt" border-top-width="0.50pt"?>


=== Body (what gets parsed) ===
'<document>\n    <title>Test Document</title>\n    <p>Some text content</p>\n</document>'

Body content:
<document>
    <title>Test Document</title>
    <p>Some text content</p>
</document>

✅ Processing instruction is in the preamble (will be preserved)
✅ 'cell' attribute is in the preamble
=== Final reconstructed XML ===
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE document>
<?PubTbl cell border-left-width="0.50pt" border-right-width="0.50pt" border-top-width="0.50pt"?>
<document>
    <title>[TRANSLATED] Test Document</title>
    <p>[TRANSLATED] Some text content</p>
</document>

✅ 'cell' attribute preserved correctly
