# Babel Service - XML Translation Tool

A powerful service for translating XML/HTML content while preserving tags and handling special attribute encodings (JSON, pipe-delimited).

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Project Structure](#project-structure)
- [Installation](#installation)
- [Usage](#usage)
- [Configuration](#configuration)
- [How It Works](#how-it-works)
- [Advanced Usage](#advanced-usage)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)

## Overview

Babel Service is a specialized translation tool designed to handle XML and HTML content. Unlike standard translation services, it preserves the structure of the original document, including all tags and attributes, while translating only the text content. It also handles complex nested structures and specially encoded attributes (JSON, pipe-delimited).

## Features

- **Structure Preservation**: Maintains all XML/HTML tags and structure during translation
- **Special Attribute Handling**: 
  - JSON-encoded attributes
  - Pipe-delimited attributes
  - Nested attributes
- **Configurable Translation**: Dictionary-based configuration for fine-grained control over what gets translated
- **Multiple Translation Modes**: Support for different translation quality levels
- **Extensible Architecture**: Easy to add support for new tag types and attribute formats

## Project Structure

```
babel-service/
├── config/                 # Configuration files for different environments
├── docs/                   # Documentation
│   ├── getting_started.md  # Quick start guide
│   ├── xml_translation.md  # XML translation module documentation
│   └── INDEX.md            # Documentation index
├── scripts/                # Utility scripts
├── src/                    # Source code
│   ├── app.py              # Main application entry point
│   ├── dict.json           # Dictionary configuration for tag translation rules
│   ├── textual.py          # Text translation module
│   ├── xml.py              # XML translation module
│   └── request_logger.py   # Logging utilities
├── test/                   # Test files and test data
├── utils/                  # Utility modules
├── .env                    # Environment variables (not in version control)
├── config.ini              # Main configuration
├── CONTRIBUTING.md         # Contribution guidelines
├── requirements.in         # Direct dependencies
└── requirements.txt        # Complete dependencies (generated)
```

For more detailed documentation, see the [Documentation Index](docs/INDEX.md).

## Installation

### Prerequisites

- Python 3.8 or higher
- pip and pip-tools

### Setup

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd babel-service
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   # On Windows
   venv\Scripts\activate
   # On Unix or MacOS
   source venv/bin/activate
   ```

3. Install dependencies:
   ```bash
   pip install pip-tools
   pip-sync
   ```

## Usage

### Basic Usage

To translate an XML file:

```python
from src.xml import XMLTranslator
from utils.core import Mode

# Initialize the translator
translator = XMLTranslator()

# Translate XML content
with open('input.xml', 'r', encoding='utf-8') as file:
    xml_content = file.read()

# Create a dictionary with the XML content
texts_to_translate = {"xml_content": xml_content}

# Translate from English to German and Spanish
translated_texts = translator.translate(
    texts_to_translate, 
    "en", 
    ["de", "es"], 
    mode=Mode.STANDARD_TURBO
)

# Access translated content
german_translation = translated_texts["xml_content"]["de"]
spanish_translation = translated_texts["xml_content"]["es"]

# Save translations
with open('output_de.xml', 'w', encoding='utf-8') as file:
    file.write(german_translation)
```

### Command Line Usage

You can also run the XML translator directly:

```bash
python -m src.xml
```

This will translate the test XML file and save the output to the test directory.

## Configuration

### Translation Dictionary

The `dict.json` file controls which tags and attributes are translated. Each entry specifies:

- Whether a tag is translatable
- Whether it's self-closing
- Which attributes should be translated
- How attributes are encoded (JSON, pipe-delimited, etc.)

Example configuration for a tag:

```json
"elx_button": {
    "translatable": true,
    "self_closing": true,
    "attributes": {
        "button_text": {
            "translatable": true,
            "encoding": false,
            "delimited_encoding": false,
            "not_encoding": true,
            "notes": "",
            "attributes": {}
        },
        "button_link": {
            "translatable": false,
            "encoding": false,
            "delimited_encoding": true,
            "not_encoding": false,
            "notes": "",
            "attributes": {
                "title": {
                    "translatable": true,
                    "encoding": false,
                    "delimited_encoding": true,
                    "not_encoding": false,
                    "notes": "",
                    "attributes": {}
                }
            }
        }
    }
}
```

### Environment Configuration

Create a `.env` file with your Azure Translator API credentials:

```
TRANSLATOR_KEY=your_translator_key
TRANSLATOR_REGION=your_region
```

## How It Works

The translation process follows these steps:

1. **Parsing**: The XML content is parsed and tags are identified
2. **Placeholder Substitution**: Tags are replaced with placeholders to protect them during translation
3. **Attribute Processing**: Attributes are processed based on the dictionary configuration:
   - Regular attributes are translated directly
   - JSON-encoded attributes are decoded, translated, and re-encoded
   - Pipe-delimited attributes are split, translated, and rejoined
4. **Text Translation**: The text with placeholders is sent to the translation service
5. **Reconstruction**: The translated text is reconstructed by replacing placeholders with the original (or translated) tags

## Advanced Usage

### Custom Tag Handling

To add support for a new tag type, add an entry to the `dict.json` file:

```json
"my_custom_tag": {
    "translatable": true,
    "self_closing": false,
    "attributes": {
        "title": {
            "translatable": true,
            "encoding": false,
            "delimited_encoding": false,
            "not_encoding": true,
            "notes": "",
            "attributes": {}
        }
    }
}
```

### Translation Modes

The service supports different translation modes:

- `Mode.STANDARD_TURBO`: Fast translation with good quality
- `Mode.STANDARD_PROOFREAD`: Higher quality translation with proofreading
- `Mode.STANDARD_CREATIVE`: Creative translation for marketing content

## Troubleshooting

### Common Issues

- **Missing Tags in Output**: Check if the tag is properly defined in `dict.json`
- **Untranslated Attributes**: Verify the attribute is marked as `"translatable": true`
- **JSON Parsing Errors**: Ensure JSON-encoded attributes are properly formatted

### Logging

The service logs detailed information to `app.log`. Check this file for debugging information.

## Contributing

### Managing Dependencies

Dependencies are managed using `pip-tools`:

1. Add a dependency to `requirements.in`
2. Run `pip-compile requirements.in` to update `requirements.txt`
3. Run `pip-sync` to install the updated dependencies

Do not modify `requirements.txt` directly as it is automatically managed by pip-tools.

### Code Style

Follow PEP 8 guidelines and include docstrings for all functions and classes.

### Testing

Add tests for new features in the `test` directory and ensure all existing tests pass before submitting changes.

For more detailed contribution guidelines, see [CONTRIBUTING.md](CONTRIBUTING.md).
