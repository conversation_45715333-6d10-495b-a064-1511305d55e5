#!/usr/bin/env python3
"""
Test script for the new batch proofreading functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.textual import TextTranslator
from utils.core import Mode, Language, LocalizedText
from utils.clients import ProofreaderClient
from azure.ai.translation.text.models import TranslationLanguage

def test_batch_proofread_client():
    """Test the ProofreaderClient batch functionality directly."""
    print("Testing ProofreaderClient batch functionality...")
    
    try:
        # Create mock language objects
        en_lang = Language("en", TranslationLanguage(name="English", native_name="English"))
        de_lang = Language("de", TranslationLanguage(name="German", native_name="Deutsch"))
        
        # Create test data
        source_texts = [
            LocalizedText("Hello world", en_lang),
            LocalizedText("This is a test", en_lang),
            LocalizedText("Electrolux Professional equipment", en_lang),
        ]
        
        target_texts = [
            LocalizedText("Hallo Welt", de_lang),
            LocalizedText("Das ist ein Test", de_lang),
            LocalizedText("Electrolux Professional Ausrüstung", de_lang),
        ]
        
        # Test batch review
        client = ProofreaderClient(verify=False)  # Use verify=False for testing
        
        print(f"Input texts: {len(source_texts)}")
        for i, (src, tgt) in enumerate(zip(source_texts, target_texts)):
            print(f"  {i+1}. '{src.text}' -> '{tgt.text}'")
        
        reviewed = client.review_translation_batch(source_texts, target_texts)
        
        print(f"\nReviewed texts: {len(reviewed)}")
        for i, reviewed_text in enumerate(reviewed):
            print(f"  {i+1}. '{reviewed_text.text}'")
        
        print("✓ Batch proofreading test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Batch proofreading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_text_translator_batch_mode():
    """Test the TextTranslator with batch proofread mode."""
    print("\nTesting TextTranslator with batch proofread mode...")
    
    try:
        translator = TextTranslator(verify=False)
        
        # Test data
        texts_to_translate = {
            "text1": "Hello world",
            "text2": "This is a test",
            "text3": "Electrolux Professional equipment is excellent",
        }
        
        print(f"Translating {len(texts_to_translate)} texts with STANDARD_PROOFREAD_BATCH mode...")
        
        # Test batch proofread mode
        result = translator.translate(
            texts_to_translate,
            source="en",
            target=["de"],
            mode=Mode.STANDARD_PROOFREAD_BATCH
        )
        
        print("Translation results:")
        for text_id, translations in result.items():
            for lang, translation in translations.items():
                print(f"  {text_id} ({lang}): '{translation}'")
        
        print("✓ TextTranslator batch mode test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ TextTranslator batch mode test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_empty_texts():
    """Test handling of empty texts in batch mode."""
    print("\nTesting empty text handling...")
    
    try:
        # Create mock language objects
        en_lang = Language("en", TranslationLanguage(name="English", native_name="English"))
        de_lang = Language("de", TranslationLanguage(name="German", native_name="Deutsch"))
        
        # Test with some empty texts
        source_texts = [
            LocalizedText("", en_lang),
            LocalizedText("This is a test", en_lang),
            LocalizedText("", en_lang),
        ]
        
        target_texts = [
            LocalizedText("", de_lang),
            LocalizedText("Das ist ein Test", de_lang),
            LocalizedText("", de_lang),
        ]
        
        client = ProofreaderClient(verify=False)
        reviewed = client.review_translation_batch(source_texts, target_texts)
        
        print(f"Reviewed {len(reviewed)} texts (including empty ones)")
        for i, reviewed_text in enumerate(reviewed):
            print(f"  {i+1}. '{reviewed_text.text}' (empty: {len(reviewed_text.text) == 0})")
        
        print("✓ Empty text handling test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Empty text handling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Running batch proofreading tests...\n")
    
    # Run tests
    tests = [
        test_batch_proofread_client,
        test_text_translator_batch_mode,
        test_empty_texts,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        sys.exit(0)
    else:
        print("❌ Some tests failed!")
        sys.exit(1)
