#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile requirements.in
#
aniso8601==10.0.0
    # via flask-restful
annotated-types==0.7.0
    # via pydantic
anyio==4.8.0
    # via
    #   httpx
    #   openai
azure-ai-translation-text==1.0.1
    # via -r requirements.in
azure-common==1.1.28
    # via azure-search-documents
azure-core==1.32.0
    # via
    #   azure-ai-translation-text
    #   azure-search-documents
azure-search-documents==11.5.3
    # via -r requirements.in
backoff==2.2.1
    # via -r requirements.in
blinker==1.9.0
    # via flask
build==1.2.2.post1
    # via pip-tools
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via
    #   flask
    #   pip-tools
colorama==0.4.6
    # via
    #   build
    #   click
    #   tqdm
distro==1.9.0
    # via openai
flask==3.1.0
    # via
    #   -r requirements.in
    #   flask-restful
flask-restful==0.3.10
    # via -r requirements.in
h11==0.14.0
    # via httpcore
httpcore==1.0.7
    # via httpx
httpx==0.28.1
    # via openai
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
isodate==0.7.2
    # via
    #   azure-ai-translation-text
    #   azure-search-documents
itsdangerous==2.2.0
    # via flask
jinja2==3.1.5
    # via flask
jiter==0.8.2
    # via openai
lxml==5.3.0
    # via -r requirements.in
markupsafe==3.0.2
    # via
    #   jinja2
    #   werkzeug
marshmallow==3.26.1
    # via -r requirements.in
openai==1.60.2
    # via -r requirements.in
packaging==24.2
    # via
    #   build
    #   marshmallow
pip-tools==7.4.1
    # via -r requirements.in
pydantic==2.10.6
    # via openai
pydantic-core==2.27.2
    # via pydantic
pyproject-hooks==1.2.0
    # via
    #   build
    #   pip-tools
pytz==2025.1
    # via flask-restful
regex==2024.11.6
    # via tiktoken
requests==2.32.3
    # via
    #   azure-core
    #   tiktoken
six==1.17.0
    # via
    #   azure-core
    #   flask-restful
sniffio==1.3.1
    # via
    #   anyio
    #   openai
tiktoken==0.9.0
    # via -r requirements.in
tqdm==4.67.1
    # via openai
typing-extensions==4.12.2
    # via
    #   anyio
    #   azure-ai-translation-text
    #   azure-core
    #   azure-search-documents
    #   openai
    #   pydantic
    #   pydantic-core
urllib3==2.3.0
    # via requests
werkzeug==3.1.3
    # via flask
wheel==0.45.1
    # via pip-tools

# The following packages are considered to be unsafe in a requirements file:
# pip
# setuptools
