import logging
import os
import sys
import io
from contextlib import redirect_stdout

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from azure.search.documents.indexes import SearchIndexClient, SearchIndexerClient
from azure.core.credentials import AzureKeyCredential
from azure.search.documents.indexes.models import (
    SearchIndexerDataContainer,
    SearchIndexerDataSourceConnection,
    SearchIndex,
    SearchIndexer,
    SimpleField,
    SearchFieldDataType,
)


class IndexManager:
    def __init__(self, search_client=None, indexer_client=None):
        """
        Initialize IndexManager with optional clients.
        
        Args:
            search_client: Azure SearchIndexClient instance
            indexer_client: Azure SearchIndexerClient instance
        """
        if search_client and indexer_client:
            self.search_client = search_client
            self.indexers_client = indexer_client
        else:
            # Fallback to environment variables
            azure_search_endpoint = os.getenv("AZURE_SEARCH_SERVICE_ENDPOINT")
            azure_search_key = os.getenv("AZURE_SEARCH_SERVICE_KEY")
            
            self.search_client = SearchIndexClient(
                azure_search_endpoint,
                AzureKeyCredential(azure_search_key)
            )
            self.indexers_client = SearchIndexerClient(
                azure_search_endpoint,
                AzureKeyCredential(azure_search_key)
            )

    @classmethod
    def from_rag_client(cls, rag_client=None) -> 'IndexManager':
        """
        Create IndexManager instance using the shared clients from RAGClient.
        
        Args:
            rag_client: RAGClient instance (optional, will create singleton if None)
            
        Returns:
            IndexManager: Instance using shared clients from RAGClient
        """
        if rag_client is None:
            # Add the webservices directory to path for imports
            webservices_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
            if webservices_path not in sys.path:
                sys.path.append(webservices_path)
            
            from utils.clients import RAGClient
            rag_client = RAGClient()
        
        # Use the existing clients from RAGClient (no duplication)
        return cls(
            search_client=rag_client.search_index_client,
            indexer_client=rag_client.search_indexer_client
        )

    def list_indexes(self):
        """
        List all search indexes.
        
        Returns:
            list: List of SearchIndex objects
        """
        try:
            result = self.search_client.list_indexes()
            return list(result)
        except Exception as e:
            logging.error(f"Error listing indexes: {e}")
            return []

    def get_all_index_names(self):
        """
        Get a simple list of all index names.
        
        Returns:
            list: List of index names as strings
        """
        try:
            indexes = self.search_client.list_indexes()
            return [index.name for index in indexes]
        except Exception as e:
            logging.error(f"Error getting index names: {e}")
            return []

    def get_index(self, index_name):
        """
        Fetches a specific index by name.
        
        Args:
            index_name (str): The name of the index to retrieve.
            
        Returns:
            SearchIndex: The index object if found, else None.
        """
        try:
            index = self.search_client.get_index(index_name)
            return index
        except Exception as e:
            logging.error(f"Error fetching index '{index_name}': {e}")
            return None

    def get_index_statistics(self, index_name):
        """
        Get statistics for a specific index.
        
        Args:
            index_name (str): The name of the index
            
        Returns:
            dict: Index statistics including document count and storage size
        """
        try:
            stats = self.search_client.get_index_statistics(index_name)
            return {
                'document_count': stats.document_count,
                'storage_size': stats.storage_size
            }
        except Exception as e:
            logging.error(f"Error getting statistics for index '{index_name}': {e}")
            return None

    def list_indexers(self):
        """
        List all indexers.
        
        Returns:
            list: List of SearchIndexer objects
        """
        try:
            result = self.indexers_client.get_indexers()
            return list(result)
        except Exception as e:
            logging.error(f"Error listing indexers: {e}")
            return []
    
            
def main():
    """Main execution function."""
    # Suppress output during RAGClient initialization
    with redirect_stdout(io.StringIO()):
        index_client = IndexManager.from_rag_client()
    
    print("📋 List of Index Names:")
    index_names = index_client.get_all_index_names()
    
    if index_names:
        for i, name in enumerate(index_names, 1):
            print(f"  {i}. {name}")
        print(f"\nTotal indexes found: {len(index_names)}")
    else:
        print("  No indexes found.")

    index_names = "tm-groundtruth-en-it"
    print("\n🔍 Index Statistics:")
    print(index_client.get_index_statistics(index_names))



if __name__ == "__main__":
    main()





