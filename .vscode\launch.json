{
    // Usare IntelliSense per informazioni sui possibili attributi.
    // Al passaggio del mouse vengono visualizzate le descrizioni degli attributi esistenti.
    // Per altre informazioni, visitare: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debugger Python: API Translator",
            "type": "debugpy",
            "request": "launch",
            "module": "flask",
            "env": {
                "FLASK_APP": "src.app",
                "FLASK_DEBUG": "1"
            },
            "args": [
                "run",
                "--debug",
                "--port=8000"
                //"--host=0.0.0.0",
                //"--no-reload"
            ],
            "jinja": false,
            //"preLaunchTask": "prepare_environment",
            "envFile":"${workspaceFolder}/.env",
            "python": "${workspaceFolder}/venv/Scripts/python.exe",
        },
        {
            "name": "Debugger Python: Test XML",
            "type": "debugpy",
            "request": "launch",
            "module": "src.xml",
            "cwd": "${workspaceFolder}",
            "justMyCode": false,
            "envFile": "${workspaceFolder}/.env",
            "python": "${workspaceFolder}/venv/Scripts/python.exe",
            // "env": {
            //     "PYTHONPATH": "${workspaceFolder}",
            //     "REQUESTS_CA_BUNDLE": "${workspaceFolder}/certificates/zscaler.pem",
            //     "SSL_CERT_FILE": "${workspaceFolder}/certificates/zscaler.pem"
            // }
        }
    ]
}