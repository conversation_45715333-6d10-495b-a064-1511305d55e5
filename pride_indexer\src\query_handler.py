from azure.search.documents import Search<PERSON>lient
from azure.core.credentials import AzureKeyCredential
from typing import List, Dict, Optional, TYPE_CHECKING
from utils.core import LocalizedText

# Use TYPE_CHECKING to avoid circular import during runtime
if TYPE_CHECKING:
    from utils.clients import EmbedderClient


class FewShotProvider :
    """
    Azure AI Search utility class for performing various search operations.
    
    This class provides text, vector, and hybrid search capabilities for retrieving
    relevant translation examples from Azure AI Search indices.
    """
    
    def __init__(self, search_endpoint: str, search_index: str, search_key: str, 
                 embedder_client: 'EmbedderClient', k: int = 5):
        """
        Initialize Azure Search AI client.
        
        Args:
            search_endpoint (str): Azure AI Search service endpoint
            search_index (str): Name of the search index
            search_key (str): API key for Azure AI Search
            embedder_client (EmbedderClient): Client for generating embeddings
            k (int): Number of results to retrieve
        """
        self.__search = SearchClient(
            endpoint=search_endpoint,
            index_name=search_index,
            credential=AzureKeyCredential(search_key)
        )
        self.__embedder_client = embedder_client
        self.k = k
    
    @classmethod
    def from_rag_client(cls, rag_client=None) -> 'FewShotProvider ':
        """
        Create AzureSearchAI instance using the shared SearchClient from RAGClient.
        
        Args:
            rag_client: RAGClient instance (optional, will create singleton if None)
            
        Returns:
            AzureSearchAI: Instance using shared SearchClient
        """
        if rag_client is None:
            from utils.clients import RAGClient
            rag_client = RAGClient()
        
        # Return the existing instance from RAGClient (no duplication)
        return rag_client.azure_search
    
    def build_filter(self, description_type: str, brand: Optional[LocalizedText] = None, 
                    type_: Optional[LocalizedText] = None) -> str:
        """
        Build OData filter string for Azure Search based on description type and metadata.
        
        Args:
            description_type (str): Type of description ("short" or "long")
            brand (LocalizedText, optional): Brand name for filtering
            type_ (LocalizedText, optional): Product type for filtering
            
        Returns:
            str: OData filter string
        """
        clauses = [f"description_type eq '{description_type}'"]

        # Add brand/type filtering for long descriptions
        if description_type == "long":
            if brand and brand.text.lower() != "null" and brand.text.isdigit():
                clauses.append(f"brand eq {brand.text}")
            if type_ and type_.text.lower() != "null" and type_.text.isdigit():
                clauses.append(f"type eq {type_.text}")

        return " and ".join(clauses)

    def text_search_examples(self, source_text: str, filter_str: str) -> List[Dict[str, str]]:
        """
        Perform BM25 text-based search for finding relevant translation examples.
        
        Best for:
        - Exact keyword matching
        - Brand/product name searches
        - Technical term matching
        - Long descriptions with specific terminology
        
        Args:
            source_text (str): Source text for keyword matching
            filter_str (str): OData filter string
            
        Returns:
            list: Translation examples from text search
        """
        examples = []
        
        try:
            results = self.__search.search(
                search_text=source_text,           # Use source text for keyword matching
                filter=filter_str,
                top=self.k,
                select=["source_text", "target_text", "description_type"]
            )
            
            for result in results:
                examples.append({
                    "source": result['source_text'],
                    "target": result['target_text']
                })
                
        except Exception:
            # Graceful degradation: continue with empty examples if search fails
            pass
        
        return examples

    def vector_search_examples(self, query_text: str, filter_str: str) -> List[Dict[str, str]]:
        """
        Perform vector similarity search for finding semantically related translation examples.
        
        Best for:
        - Semantic similarity matching
        - Conceptually related terms
        - Short descriptions
        - Cross-linguistic semantic understanding
        
        Args:
            source_text (str): Source text for semantic similarity
            filter_str (str): OData filter string
            
        Returns:
            list: Translation examples from vector search
        """
        examples = []
        
        try:
            # Generate embeddings for semantic similarity search using dedicated client
            vectorized_query_text = self.__embedder_client.generate_embedding_batch(query_text)

            # Perform vector search to find semantically similar translations
            results = self.__search.search(
                search_text="*",                   # Wildcard for pure vector search
                vector_queries=[{
                    "kind": "vector",  # Vector query type
                    "vector": vectorized_query_text, #  vector for search
                    "fields": "source_text_vector",  # Field to search against
                    "k_nearest_neighbors": self.k # Number of nearest neighbors to retrieve
                }],
                filter=filter_str,
                select=["source_text", "target_text", "description_type"]
            )
            
            for result in results:
                examples.append({
                    "source": result['source_text'],
                    "target": result['target_text']
                })
                
        except Exception:
            # Graceful degradation: continue with empty examples if search fails
            pass
        
        return examples

    def hybrid_search_examples(self, query_text: str, filter_str: str) -> List[Dict[str, str]]:
        """
        Perform hybrid search combining both text and vector search for comprehensive results.
        
        Best for:
        - Maximum coverage and relevance
        - Complex queries requiring both keyword and semantic matching
        - When unsure which search strategy is optimal
        - Critical translations requiring diverse examples
        
        Args:
            source_text (str): Source text for both keyword and semantic matching
            filter_str (str): OData filter string
            
        Returns:
            list: Combined and deduplicated translation examples from both search methods
        """
        examples = []
        
        try:
            # Generate embeddings for vector component using dedicated client
            vectorized_query_text = self.__embedder_client.generate_embedding_batch(query_text)

            # Perform hybrid search combining text and vector queries
            results = self.__search.search(
                search_text=query_text,           # Text component for keyword matching
                vector_queries=[{                  # Vector component for semantic similarity
                    "kind": "vector", 
                    "vector": vectorized_query_text,
                    "fields": "source_text_vector", 
                    "k_nearest_neighbors": self.k
                }],
                filter=filter_str,
                top=self.k,                        # Get top results from combined ranking
                select=["source_text", "target_text", "description_type"]
            )
            
            # Azure AI Search automatically combines and ranks text + vector results
            for result in results:
                examples.append({
                    "source": result['source_text'],
                    "target": result['target_text']
                })
                
        except Exception:
            # Graceful degradation: fall back to text search only
            try:
                return self.text_search_examples(query_text, filter_str)
            except Exception:
                pass
        
        return examples

    def get_few_shot_examples(self, query_text: str, description_type: str, 
                             search_type: str = "auto", brand: Optional[LocalizedText] = None,
                             type_: Optional[LocalizedText] = None) -> List[Dict[str, str]]:
        """
        Retrieve translation examples based on the source text and description type.
        
        Args:
            source_text (str): Source text to search for
            description_type (str): Type of description ("short" or "long")
            search_type (str): Search strategy - "text_search", "vector_search", "hybrid_search", or "auto"
            brand (LocalizedText, optional): Brand name for filtering
            type_ (LocalizedText, optional): Product type for filtering
            
        Returns:
            list: Translation examples from the selected search strategy
        """
        filter_str = self.build_filter(description_type, brand, type_)
        
        # Determine search strategy
        if search_type == "auto":
            # Use original logic: vector for short, text for long
            if description_type == "short":
                return self.vector_search_examples(query_text, filter_str)
            else:
                return self.text_search_examples(query_text, filter_str)
        elif search_type == "text_search":
            return self.text_search_examples(query_text, filter_str)
        elif search_type == "vector_search":
            return self.vector_search_examples(query_text, filter_str)
        elif search_type == "hybrid_search":
            return self.hybrid_search_examples(query_text, filter_str)
        else:
            # Default to vector search for invalid search_type
            return self.vector_search_examples(query_text, filter_str)
        

