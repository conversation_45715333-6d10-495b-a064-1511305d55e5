{"laundry-cycle-management": {"translatable": false, "self_closing": true, "attributes": {}}, "vc_row": {"translatable": false, "self_closing": false, "attributes": {}}, "vc_column": {"translatable": false, "self_closing": false, "attributes": {}}, "vc_row_inner": {"translatable": false, "self_closing": false, "attributes": {}}, "vc_column_inner": {"translatable": false, "self_closing": false, "attributes": {}}, "vc_column_text": {"translatable": true, "self_closing": false, "attributes": {"content": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "already out of square brackets", "attributes": {}}}}, "vc_zigzag": {"translatable": false, "self_closing": false, "attributes": {}}, "vc_hoverbox": {"translatable": true, "self_closing": false, "attributes": {"primary_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "content": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "already out of square brackets", "attributes": {}}}}, "vc_single_image": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "vc_tta_tabs": {"translatable": true, "self_closing": false, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "vc_tta_section": {"translatable": true, "self_closing": false, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "vc_tta_accordion": {"translatable": true, "self_closing": false, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "vc_tta_toggle": {"translatable": true, "self_closing": false, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "vc_pricing_table": {"translatable": true, "self_closing": false, "attributes": {"subheading": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "currency": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "price": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "period": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "btn_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "vc_raw_html": {"translatable": false, "self_closing": false, "attributes": {}}, "vc_raw_js": {"translatable": false, "self_closing": false, "attributes": {}}, "vc_empty_space": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_hero_slider": {"translatable": true, "self_closing": false, "attributes": {"label": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_hero_title label": {"translatable": true, "self_closing": false, "attributes": {"label": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "video_link": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "elx_hero_video": {"translatable": false, "self_closing": false, "attributes": {}}, "elx_simple_block": {"translatable": false, "self_closing": false, "attributes": {}}, "elx_simple_link_block": {"translatable": true, "self_closing": false, "attributes": {"link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_image_block": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_video_block": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_button": {"translatable": true, "self_closing": true, "attributes": {"button_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "button_link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_floating_icon": {"translatable": true, "self_closing": true, "attributes": {"scroll_to": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_heading": {"translatable": true, "self_closing": false, "attributes": {"content": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "already out of square brackets", "attributes": {}}}}, "elx_hint": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "details": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_step": {"translatable": true, "self_closing": true, "attributes": {"number": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "details": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_citation": {"translatable": true, "self_closing": false, "attributes": {"name": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "company": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_icons": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_maylike_box": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_download_block": {"translatable": true, "self_closing": true, "attributes": {"text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_social_share": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_menu": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_o2000_locator": {"translatable": true, "self_closing": true, "attributes": {"request": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_o2000_product_registration": {"translatable": true, "self_closing": true, "attributes": {}}, "elx_o2000_speedelight_image_tool": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_o2000_speedelight_calculator": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_o2000_search_documentation": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_o2000_cutting_guide": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_o2000_empower": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_o2000_refrigeration_calculator": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_o2000_warewashing_matcher": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_o2000_host_2017_form": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_o2000_contact_us": {"translatable": true, "self_closing": true, "attributes": {"s": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_send_button": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "thanks": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "r": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "side_banner_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_btn_contact": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_o2000_skyline_recipe_images": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_image_text_box": {"translatable": true, "self_closing": true, "attributes": {"image_text_block": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_small_text_box": {"translatable": true, "self_closing": true, "attributes": {"text_box": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_step_banner": {"translatable": true, "self_closing": true, "attributes": {"title_box": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_box": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "cta": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_o2000_clarusvibe_resize_tool": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_search": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_last_news": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_last_references": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_full_banner": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_single_banner": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_mobile": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_double_banner": {"translatable": true, "self_closing": true, "attributes": {"title_1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_mobile_1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link_1": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "title_2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_mobile_2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link_2": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_triple_banner": {"translatable": true, "self_closing": true, "attributes": {"title_1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_3": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_3": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link_1": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "link_2": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "link_3": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_active_banner": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_master_slider": {"translatable": true, "self_closing": true, "attributes": {"title_1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_3": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_3": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_4": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_4": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link_1": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "link_2": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "link_3": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "link_4": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_list_support_customers": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_carousel": {"translatable": true, "self_closing": true, "attributes": {"top_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_3": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_4": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link_1": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "link_2": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "link_3": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "link_4": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_image": {"translatable": true, "self_closing": true, "attributes": {"link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_icons_box": {"translatable": true, "self_closing": true, "attributes": {"text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_showcase": {"translatable": false, "self_closing": false, "attributes": {}}, "elx_single_card": {"translatable": true, "self_closing": true, "attributes": {"date": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_shop_now": {"translatable": true, "self_closing": true, "attributes": {"button_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "button_link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_video_text": {"translatable": true, "self_closing": false, "attributes": {"link_video": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "content": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "already out of square brackets", "attributes": {}}}}, "elx_o2000_speedelight_calculator_dryers": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_o2000_ecostore_energy_saving_calculator": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_o2000_electric_vs_hp_dryer_calculator": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_o2000_mix_up_calculator": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_o2000_thermaline_braising_pans": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_multiple_columns_features": {"translatable": true, "self_closing": true, "attributes": {"values": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"title": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "text": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_multiple_columns": {"translatable": true, "self_closing": false, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "values": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"title_section": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "text_section": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "note_section": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "link_section": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}}}, "elx_text_image": {"translatable": true, "self_closing": false, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "subtitle": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "lists": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"title_list": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "text_list": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "values": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"title_icon": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "text_icon": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_features_download": {"translatable": true, "self_closing": false, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_download": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_discover": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "values": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"link_download": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "discover": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"link_discover": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "features": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"title_icon": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "text_icon": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "content": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "already out of square brackets", "attributes": {}}, "link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "link_column_box": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_image_description": {"translatable": true, "self_closing": false, "attributes": {"content": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "already out of square brackets", "attributes": {}}}}, "elx_product_showcase": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_col1": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "subtitle_col1": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "link_col1": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "title_col2": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "subtitle_col2": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "link_col2": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "title_col3": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "subtitle_col3": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "link_col3": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "title_col4": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "subtitle_col4": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "link_col4": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_image_text_carousel": {"translatable": true, "self_closing": true, "attributes": {"slides": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"title": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "c21_slides_text": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_o2000_needs_matcher": {"translatable": true, "self_closing": false, "attributes": {}}, "elx_testimonial": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "personal_info": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "content": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "already out of square brackets", "attributes": {}}}}, "elx_multiple_columns_features_gif": {"translatable": true, "self_closing": false, "attributes": {"values": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"title": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "text": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_benefit": {"translatable": true, "self_closing": true, "attributes": {"label_1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_1_1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_1_1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_1_2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_1_2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_1_3": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_1_3": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "label_2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_2_1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_2_1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_2_2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_2_2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_2_3": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text_2_3": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_infografic": {"translatable": true, "self_closing": true, "attributes": {"title_info_1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_info_2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_info_3": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_info_4": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_info_5": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_info_6": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "product_1": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"product": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "product_2": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"product": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "product_3": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"product": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "product_4": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"product": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "product_5": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"product": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "product_6": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"product": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}}}, "elx_navigation_branch": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_last_blog": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_virtual_view": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_promo_video": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_promo_2_3_columns_and_icons": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "note": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "col1_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "col1_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "col2_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "col2_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "col3_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "col3_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_promo_why_2_3_columns_texts_icons": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "col1_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "col1_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "col2_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "col2_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "col3_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "col3_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_promo_text_image_cta": {"translatable": true, "self_closing": false, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "pretitle": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_promo_top_banner": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "carousel": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"title_carousel": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "text_carousel": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "link_carousel": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "link": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"link_download": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}}}, "elx_promo_download_leaflet": {"translatable": true, "self_closing": false, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "pretitle": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link_download": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "content": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "already out of square brackets", "attributes": {}}}}, "elx_promo_other_promotions": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_col1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link_col1": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "title_col2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link_col2": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "title_col3": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link_col3": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "title_col4": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link_col4": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_promo_carousel_images": {"translatable": true, "self_closing": true, "attributes": {"promo_carousel": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"text": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_promo_products_showcase": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "title_col1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "prodcode_col1": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "price_col1": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "desc_col1": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link_col1": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "title_col2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "prodcode_col2": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "price_col2": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "desc_col2": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "link_col2": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "title_col3": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "prodcode_col3": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "price_col3": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "desc_col3": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "link_col3": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_carousel_steps": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "cta_link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "card1_icon_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card1_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card1_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card1_link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "card2_icon_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card2_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card2_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card2_link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "card3_icon_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card3_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card3_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card3_link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "card4_icon_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card4_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card4_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card4_link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "card5_icon_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card5_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card5_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card5_link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "card6_icon_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card6_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card6_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card6_link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "card7_icon_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card7_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card7_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card7_link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "card8_icon_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card8_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card8_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card8_link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "card9_icon_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card9_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card9_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card9_link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}, "card10_icon_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card10_title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card10_text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card10_link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_certification_carousel": {"translatable": true, "self_closing": true, "attributes": {"card_list": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "cta_link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}}}, "elx_o2000_document_library": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_promo_top_banner_full_image": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "link": {"translatable": true, "encoding": false, "delimited_encoding": true, "not_encoding": false, "notes": "", "attributes": {"url": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_promo_contacts": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "card": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "text": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "day": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "hours": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "phone": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "email": {"translatable": false, "encoding": false, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_promo_faq": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "values": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {"question": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}, "answer": {"translatable": true, "encoding": true, "delimited_encoding": false, "not_encoding": false, "notes": "", "attributes": {}}}}}}, "elx_last_recipes": {"translatable": true, "self_closing": true, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_o2000_seminars_form_italian_version": {"translatable": true, "self_closing": true, "attributes": {"emailsubject": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "email": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_o2000_food_repair_service": {"translatable": true, "self_closing": true, "attributes": {"email": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "emailsubject": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_o2000_events_locator": {"translatable": false, "self_closing": true, "attributes": {}}, "elx_o2000_laundry_service_form": {"translatable": true, "self_closing": true, "attributes": {"emailsubject": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}, "email": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_o2000_locator_jp": {"translatable": false, "self_closing": true, "attributes": {}}, "row_accordion": {"translatable": true, "self_closing": false, "attributes": {"title": {"translatable": true, "encoding": false, "delimited_encoding": false, "not_encoding": true, "notes": "", "attributes": {}}}}, "elx_o2000_mailchimp_form": {"translatable": false, "self_closing": true, "attributes": {}}, "com21_single_product_box": {"translatable": false, "self_closing": true, "attributes": {}}, "com21_products_box": {"translatable": false, "self_closing": true, "attributes": {}}, "com21_products_navigation": {"translatable": false, "self_closing": true, "attributes": {}}, "com21_banner_accessories_box": {"translatable": false, "self_closing": true, "attributes": {}}}