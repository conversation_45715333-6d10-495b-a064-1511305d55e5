# using flask_restful
import base64, codecs
import os, logging
import datetime
import platform
from json import dumps
from logging import DEBUG, INFO, StreamHandler, basicConfig, FileHandler
from typing import Dict, Tuple, Optional
from config.core import TranslateConfig
from pathlib import Path
from flask import Flask, Response, jsonify, make_response, request
from flask_restful import Api, Resource, reqparse
from lxml.etree import XMLParser, _Element, fromstring, tostring
from werkzeug.exceptions import NotFound
from src.textual import TextTranslator
from src.xml import XMLTranslator
from src.coti_xml import COTIXMLTranslator
from src.simonsoft_xml import SimonsoftXMLTranslator
from utils.core import ClientException, Mode, Translator, get_logger
from src.request_logger import log_request
import requests, json



log_folder = Path(__file__).resolve().parent.parent / "logs"
os.makedirs(log_folder, exist_ok=True)  # Crea la cartella logs se non esiste

"""Configura il logging globale dell'applicazione"""
basicConfig(
    level=TranslateConfig().log_level,
    format="[%(asctime)s] {%(thread)d %(module)s.%(funcName)s:%(lineno)d} %(levelname)s: %(message)s",
    handlers=[
        StreamHandler(),
        FileHandler("logs/app.log")  # Aggiunge anche il log su file
    ]
)
logger = get_logger(__file__)


# creating the flask app
app = Flask(__name__)
# creating an API object
api = Api(app, default_mediatype="application/json")


def parse_translation_request():
    """Parse and validate the translation request parameters."""
    logger.debug("Parsing request...")

    parser = reqparse.RequestParser()
    parser.add_argument(
        "source",
        type=str,
        required=True,
        help="Cannot parse source language.",
    )
    parser.add_argument(
        "targets",
        type=str,
        required=True,
        help="Cannot parse targets languages.",
        action="append",
    )
    parser.add_argument(
        "texts", required=True, type=dict, help="Cannot parse provided texts."
    )
    parser.add_argument(
        "mode", default=Mode.STANDARD_PROOFREAD.name, type=str
    )
    parser.add_argument(
        "encoding", default="utf-8", type=str
    )
    parser.add_argument(
        "description_type", type=str, default="short", help="Description type for RAG mode."
    )
    parser.add_argument(
        "brand", type=str, required=False, help="Brand for RAG mode."
    )
    parser.add_argument(
        "type_", type=str, required=False, help="Type for RAG mode."
    )

    logger.debug("Request parsed!")

    return parser.parse_args()

def validate_translation_request(args, translator):
    """
    Validate translation request parameters against supported languages.

    Args:
        args: The parsed request arguments
        translator: The translator instance to use for validation

    Returns:
        tuple: (valid_targets, mode) if validation succeeds

    Raises:
        ClientException: If validation fails
    """
    try:
        codecs.lookup(args.encoding)
    except LookupError:
        return create_error_message("Invalid encoding", 422)

    if not args.texts:
        logger.info("Texts field empty.")
        return create_error_message("No texts were provided for translation", 422)

    if args.mode.upper() in Mode.__members__:
        logger.info(f"Executing in {args.mode} mode.")
        mode = Mode[args.mode.upper()]
    else:
        logger.info(f"Invalid mode value ({args.mode}).")
        return create_error_message(
            f"Invalid mode provided ({args.mode}). Please choose one of the following values: {', '.join(e.name for e in Mode)}",
            422
        )

    try:
        logger.debug("Fetching supported languages' codes...")
        supported_codes = [
            language.code for language in translator.supported_languages
        ]

        if args.mode.upper() in [Mode.STANDARD_TURBO.name, Mode.STANDARD_PROOFREAD.name]:
            supported_codes.append("auto")  # Allows 'auto' as source language to manage auto-detection

        logger.info(f"{len(supported_codes)} supported languages fetched!")
    except ClientException as e:
        logger.critical(
            f"Failed to fetch supported languages (Error {e.status} - {e.message})"
        )
        return create_error_message(e.message, e.status)

    if args.source not in supported_codes:
        logger.info(f"Source language '{args.source}' not supported.")
        return create_error_message(
            f"Source language '{args.source}' is not supported.",
            422
        )

    valid_targets = [
        lang for lang in sorted(args.targets) if lang in supported_codes
    ]
    logger.info(
        f"{len(valid_targets)} out of {len(args.targets)} target languages are valid."
    )

    if not valid_targets:
        return create_error_message(
            f"No target language in {[target for target in args.targets]} is supported.",
            422,
        )

    return valid_targets, mode

def get_languages_pretty(translator: Translator) -> Dict[str, str]:
    try:
        return make_response(
            jsonify(
                {
                    language.code: f"{language.description.name} ({language.description.native_name})"
                    for language in translator.supported_languages
                }
            ),
            200,
        )
    except ClientException as e:
        return create_error_message(e.message, 500)

def decode_base64_texts(encoded_texts: Dict[str, str], encoding: str) -> Tuple[Optional[Dict[str, str]], Optional[Response]]:
    """
    Decodifica un dizionario di stringhe Base64.

    Args:
        encoded_texts: Dizionario con chiavi e testi codificati in Base64

    Returns:
        Tuple[Dict[str, str] | None, Response | None]:
            - Se successo: (dizionario_decodificato, None)
            - Se errore: (None, response_errore)
    """
    try:
        decoded_texts = {}
        for text_id, encoded_text in encoded_texts.items():
            decoded_bytes = base64.b64decode(encoded_text)
            decoded_texts[text_id] = decoded_bytes.decode(encoding)

        logger.debug(f"Successfully decoded {len(decoded_texts)} texts")
        return decoded_texts, None

    except Exception as e:
        error_msg = f"Invalid Base64 format in texts: {str(e)}"
        logger.error(error_msg)
        return None, create_error_message(error_msg, 400)

def encode_base64_texts(texts: Dict[str, Dict[str, str]]) -> Tuple[Optional[Dict[str, Dict[str, str]]], Optional[Response]]:
    """
    Codifica un dizionario di dizionari di stringhe in Base64.

    Args:
        texts: Dizionario con struttura {id: {lingua: testo}}

    Returns:
        Tuple[Dict[str, Dict[str, str]] | None, Response | None]:
            - Se successo: (dizionario_codificato, None)
            - Se errore: (None, response_errore)
    """
    try:
        encoded_texts = {}
        for text_id, translations in texts.items():
            encoded_translations = {}
            for lang, text in translations.items():
                # Codifica il testo in Base64
                encoded_bytes = base64.b64encode(text.encode('utf-8'))
                encoded_translations[lang] = encoded_bytes.decode('utf-8')
            encoded_texts[text_id] = encoded_translations

        logger.debug(f"Successfully encoded {len(encoded_texts)} texts with their translations")
        return encoded_texts, None

    except Exception as e:
        error_msg = f"Error encoding texts to Base64: {str(e)}"
        logger.error(error_msg)
        return None, create_error_message(error_msg, 400)

def create_error_message(
    message: str, status: int, mimetype="application/json"
) -> Response:
    if mimetype.endswith("xml"):
        root = _Element("message")
        root.text = message
        output = tostring(root)
    elif mimetype.endswith("json"):
        output = dumps({"message": message})

    return Response(output, status, mimetype=mimetype)


class TranslateXML(Resource):
    translator = XMLTranslator()
    mimetype = "application/json"

    def get(self):
        return get_languages_pretty(self.translator)

    def post(self):

        args = parse_translation_request()

        validation_result = validate_translation_request(args, self.translator)
        if isinstance(validation_result, Response):
            return validation_result

        decoded_texts, decode_error = decode_base64_texts(args.texts, args.encoding)
        if decode_error:
            return decode_error

        valid_targets, mode = validation_result

        try:
            xml_translation = self.translator.translate(
                decoded_texts, args.source, valid_targets, mode=mode
            )
        except ClientException as e:
            return create_error_message(e.message, e.status, self.mimetype)


        encoded_texts, encode_error = encode_base64_texts(xml_translation)
        if encode_error:
            return encode_error

        return make_response(
            jsonify(
                {
                    "source": args.source,
                    "targets": valid_targets,
                    "mode": mode.name,
                    "translations": encoded_texts,
                }
            ),
            200,
        )

class TranslateCOTIXML(Resource):
    translator = COTIXMLTranslator(verify=False)
    mimetype = "application/json"

    def get(self):
        return get_languages_pretty(self.translator)

    def post(self):
        args = parse_translation_request()

        validation_result = validate_translation_request(args, self.translator)
        if isinstance(validation_result, Response):
            return validation_result

        decoded_texts, decode_error = decode_base64_texts(args.texts, args.encoding)
        if decode_error:
            return decode_error

        valid_targets, mode = validation_result

        try:            
            # Call the COTI XML translator
            translation = self.translator.translate(
                decoded_texts, args.source, valid_targets, mode=mode
            )
            
            # Encode results back to Base64 - translation is a dict: {text_id: {language: translated_content}}
            encoded_texts, encode_error = encode_base64_texts(translation)
            if encode_error:
                return encode_error

            return make_response(
                jsonify(
                    {
                        "source": args.source,
                        "targets": valid_targets,
                        "mode": mode.name,
                        "translations": encoded_texts,
                    }
                ),
                200,
            )
        except Exception as e:
            logger.error(f"COTI XML translation error: {str(e)}")
            return make_response(
                jsonify({"error": f"Translation failed: {str(e)}"}), 
                500
            )

class TranslateSimonsoftXML(Resource):
    translator = SimonsoftXMLTranslator()
    mimetype = "application/json"

    def get(self):
        return get_languages_pretty(self.translator)

    def post(self):
        args = parse_translation_request()
        validation_result = validate_translation_request(args, self.translator)
        if isinstance(validation_result, Response):
            return validation_result

        decoded_texts, decode_error = decode_base64_texts(args.texts, args.encoding)
        if decode_error:
            return decode_error

        valid_targets, mode = validation_result

        try:
            # For Simonsoft, expect a single XML file as text (like {"xml": "<xml...>"})
            translation = self.translator.translate_xml(
                list(decoded_texts.values())[0],  # path or xml string
                args.source,
                valid_targets,
                mode=mode
            )
            encoded_texts, encode_error = encode_base64_texts(translation)
            if encode_error:
                return encode_error

            return make_response(
                jsonify({
                    "source": args.source,
                    "targets": valid_targets,
                    "mode": mode.name,
                    "translations": encoded_texts,
                }),
                200,
            )
        except Exception as e:
            logger.error(f"Simonsoft XML translation error: {str(e)}")
            return make_response(
                jsonify({"error": f"Translation failed: {str(e)}"}),
                500
            )

class TranslateText(Resource):
    translator = TextTranslator()
    mimetype = "application/json"

    def get(self):
        return get_languages_pretty(self.translator)

    def post(self):

        args = parse_translation_request()
        validation_result = validate_translation_request(args, self.translator)
        if isinstance(validation_result, Response):
            return validation_result

        valid_targets, mode = validation_result

        # Prepare extra kwargs for STANDARD_RAG
        extra_kwargs = {}
        if mode == Mode.STANDARD_RAG:
            extra_kwargs = {
                "description_type": args.description_type,
                "brand": args.brand,
                "type_": args.type_
            }

        try:
            logger.debug(f"Translating {len(args.texts)} texts...")
            translation = self.translator.translate(
                args.texts, args.source, valid_targets, mode=mode, **extra_kwargs
            )
            logger.info(f"{len(args.texts)} texts translated!")
        except ClientException as e:
            logger.critical(f"Failed to translate (Error {e.status} - {e.message})")
            return create_error_message(e.message, e.status)

        return make_response(
            jsonify(
                {
                    "source": args.source,
                    "targets": valid_targets,
                    "mode": mode.name,
                    "translations": translation,
                }
            ),
            200,
        )

class Version(Resource):
    def get(self):
        """Endpoint API che restituisce informazioni sulla versione utilizzando variabili di Azure"""
        version_info = {
            'timestamp': datetime.datetime.now().isoformat(),
            'python_version': platform.python_version(),
            'app_static_version': TranslateConfig().app_static_version,
            'deployment': {
                'site_name': os.environ.get('WEBSITE_SITE_NAME', None),
                'hostname': os.environ.get('WEBSITE_HOSTNAME', None),
                'instance_id': os.environ.get('WEBSITE_INSTANCE_ID', None),
                'resource_group': os.environ.get('WEBSITE_RESOURCE_GROUP', None),
                'deployment_id': os.environ.get('DEPLOYMENT_ID', None),  # Puoi configurare questa variabile personalizzata
                'commit_id': os.environ.get('SCM_COMMIT_ID', None),  # Se usi Kudu deployment
            }
        }

        # In Azure DevOps pipeline, configura per impostare queste variabili personalizzate
        # durante il deploy
        custom_version = os.environ.get('APP_VERSION', None)
        build_id = os.environ.get('BUILD_BUILDID', None)
        build_number = os.environ.get('BUILD_BUILDNUMBER', None)

        if custom_version:
            version_info['app_version'] = custom_version
        if build_id:
            version_info['build_id'] = build_id
        if build_number:
            version_info['build_number'] = build_number

        return jsonify(version_info)


# adding the defined resources along with their corresponding urls
api.add_resource(TranslateText, "/v0/text")
api.add_resource(TranslateXML, "/v0/xml")
api.add_resource(Version, "/v0/version")



###############
class TranslateTextTest(Resource):

    def post(self):

        cfg = TranslateConfig()
        # Add your key and endpoint
        key = cfg.translator_key
        endpoint = cfg.text_endpoint

        # location, also known as region.
        # required if you're using a multi-service or regional (not global) resource. It can be found in the Azure portal on the Keys and Endpoint page.
        location = cfg.region

        path = '/translator/text/v3.0/translate'
        constructed_url = endpoint + path

        params = {
            'api-version': '3.0',
            'from': 'en',
            'to': ['fr', 'zu']
        }

        headers = {
            'Ocp-Apim-Subscription-Key': key,
            # location required if you're using a multi-service or regional (not global) resource.
            'Ocp-Apim-Subscription-Region': location,
            'Content-type': 'application/json',
            'X-ClientTraceId': 'abcdefgh-1234-5678-90ab-cdefghijklmn'  # Optional, for tracing
        }

        # You can pass more than one object in body.
        body = [{
            'text': 'I would really like to drive your car around the block a few times!'
        }]

        request = requests.post(constructed_url, params=params, headers=headers, json=body)
        response = request.json()

        logger.info(json.dumps(response, sort_keys=True, ensure_ascii=False, indent=4, separators=(',', ': ')))

api.add_resource(TranslateTextTest, "/v0/test")

###############
api.add_resource(TranslateCOTIXML, "/v0/coti-xml")
api.add_resource(TranslateSimonsoftXML, "/v0/simonsoft-xml")


@app.errorhandler(NotFound)
def not_found_error(error):
    return jsonify({'error': 'Not Found'}), 404


@app.errorhandler(Exception)
def handle_global_error(error):
    """
    Global error handler to catch any unhandled exceptions
    and return a 500 Internal Server Error response.
    Logs the complete exception information including traceback.
    """
    import traceback
    import sys

    # Ottieni le informazioni complete sull'eccezione
    exc_type, exc_value, exc_traceback = sys.exc_info()

    # Formatta il traceback come stringa
    exception_details = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))

    # Log dell'errore con tutti i dettagli
    logger.error(f"Unhandled Exception:\n{exception_details}")

    # Se l'errore ha attributi specifici (come nel caso di OpenAI), logghiamoli
    if hasattr(error, 'response'):
        logger.error(f"Error Response: {error.response}")

    # Se l'errore ha un messaggio specifico
    error_message = str(error) if str(error) else "An unexpected error occurred"

    return jsonify({
        "error": "Internal Server Error",
        "message": error_message,
        "type": exc_type.__name__ if exc_type else "Unknown"
    }), 500


@app.before_request
def before_request_handler():
    log_request()


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8000, debug=True)
