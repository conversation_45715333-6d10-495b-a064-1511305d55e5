"""
COTI XML Translation Module

This module provides functionality for translating COTI compliant XML translation packages
from Simonsoft CMS while preserving structure and handling translation status attributes.
"""

# Standard library imports
import os
import re
import copy
import logging
from typing import Dict, List, Optional, Union, Any, Tuple
from enum import Enum

# Third-party imports
from lxml import etree
from azure.ai.translation.text.models import TextType

# Local imports
from src.textual import TextTranslator
from utils.core import Language, Translator, Mode, get_logger
from utils.patterns import Singleton

# Configure logging
logger = get_logger(__file__)

class TranslationStatus(Enum):
    """Enumeration of COTI translation statuses"""
    RELEASED = "Released"
    IN_TRANSLATION = "In_Translation"
    REVIEW = "Review"
    NEW = "New"
    DRAFT = "Draft"
    APPROVED = "Approved"


class COTIXMLTranslator(Translator, metaclass=Singleton):
    @staticmethod
    def _sanitize_xml(xml_content: str) -> Tuple[str, dict, list]:
        """
        Remove invalid XML characters and store their positions and values for later restoration.
        Also escape invalid ampersands (&) and store their positions for later unescaping.
        Returns sanitized XML, a dict of {position: char}, and a list of ampersand positions.
        """
        import re
        # XML 1.0 valid chars: https://www.w3.org/TR/xml/#charsets
        def is_valid_xml_char(c):
            code = ord(c)
            return (
                code == 0x9 or code == 0xA or code == 0xD or
                (0x20 <= code <= 0xD7FF) or
                (0xE000 <= code <= 0xFFFD) or
                (0x10000 <= code <= 0x10FFFF)
            )
        invalids = {}
        chars = list(xml_content)
        for i, c in enumerate(chars):
            if not is_valid_xml_char(c):
                invalids[i] = c
                chars[i] = ''
        sanitized = ''.join(chars)
        # Escape invalid ampersands (not part of valid entity)
        amp_positions = []
        def amp_replacer(match):
            amp_positions.append(match.start())
            return '&amp;'
        sanitized = re.sub(r'&(?!amp;|lt;|gt;|quot;|apos;|#[0-9]+;|#x[0-9a-fA-F]+;)', amp_replacer, sanitized)
        return sanitized, invalids, amp_positions

    @staticmethod
    def _restore_invalid_chars(xml_content: str, invalids: dict, amp_positions: list) -> str:
        """
        Restore invalid characters and ampersands at their original positions in the XML string.
        """
        # Restore invalid chars
        chars = list(xml_content)
        for i, c in invalids.items():
            if i < len(chars):
                chars[i] = c
            else:
                chars.append(c)
        # Restore ampersands (replace &amp; at recorded positions with &)
        offset = 0
        for pos in amp_positions:
            idx = pos + offset
            if xml_content[idx:idx+5] == '&amp;':
                chars[idx:idx+5] = ['&']
                offset -= 4
        return ''.join(chars)
    """
    Translator for COTI compliant XML files from Simonsoft CMS.
    
    Handles mixed language content where:
    - Already translated content (cms:tstatus=Released) should be preserved
    - Content being translated should be processed
    - Translation exclusions (translate=no or markfortrans=no) should be skipped
    """
    
    def __init__(self, verify=True) -> None:
        self.translator = TextTranslator(verify=verify)
        self.translate_calls = 0
        self.processed_elements = 0
        self.skipped_elements = 0
        self.preserved_elements = 0
        
    def _increment_and_log_translate_calls(self):
        self.translate_calls += 1
        logger.debug(f"TextTranslator.translate called {self.translate_calls} times")
        
    def _reset_counters(self):
        """Reset processing counters for a new translation session"""
        self.translate_calls = 0
        self.processed_elements = 0
        self.skipped_elements = 0
        self.preserved_elements = 0

    @property
    def supported_languages(self) -> List[Language]:
        return self.translator.supported_languages

    def _should_translate_element(self, element: etree.Element) -> bool:
        """
        Determine if an element should be translated based on COTI rules.
        
        Args:
            element: The XML element to check
            
        Returns:
            bool: True if the element should be translated, False otherwise
        """
        # Check for translation exclusions
        translate_attr = element.get('translate', '').lower()
        markfortrans_attr = element.get('markfortrans', '').lower()
        
        if translate_attr == 'no' or markfortrans_attr == 'no':
            logger.debug(f"Element excluded from translation: translate={translate_attr}, markfortrans={markfortrans_attr}")
            return False
            
        # Check translation status
        tstatus = element.get('{http://www.simonsoft.se/ns/cms}tstatus', '')
        if not tstatus:
            # Also check for the attribute without namespace prefix
            tstatus = element.get('cms:tstatus', '')
            
        if tstatus == TranslationStatus.RELEASED.value:
            logger.debug(f"Element already released, preserving content: cms:tstatus={tstatus}")
            return False
            
        # Element should be translated
        logger.debug(f"Element should be translated: cms:tstatus={tstatus}")
        return True
        
    def _is_document_root_element(self, element: etree.Element) -> bool:
        """
        Check if an element is a document root element (not a graphics element).
        Graphics elements should never have their language attributes modified.
        
        Args:
            element: The XML element to check
            
        Returns:
            bool: True if this is a document root element, False if it's a graphics element
        """
        tag_name = element.tag
        if tag_name.startswith('{'):
            tag_name = tag_name.split('}')[1]
            
        # Common graphics element names that should not have language attributes changed
        graphics_elements = {
            'graphic', 'image', 'img', 'figure', 'picture', 'photo', 'illustration',
            'diagram', 'chart', 'graph', 'drawing', 'artwork', 'media', 'visual'
        }
        
        # Check if this is a graphics element
        if tag_name.lower() in graphics_elements:
            logger.debug(f"Element '{tag_name}' identified as graphics element - preserving all attributes")
            return False
            
        # Common document root elements
        document_elements = {'document', 'root', 'article', 'content', 'body', 'main'}
        
        return tag_name.lower() in document_elements
        
    def _is_graphics_element(self, element: etree.Element) -> bool:
        """
        Check if an element is a graphics element that should not be modified.
        
        Args:
            element: The XML element to check
            
        Returns:
            bool: True if this is a graphics element
        """
        tag_name = element.tag
        if tag_name.startswith('{'):
            tag_name = tag_name.split('}')[1]
            
        # Graphics elements that may carry language attributes but should NOT be changed
        graphics_elements = {
            'graphic', 'image', 'img', 'figure', 'picture', 'photo', 'illustration',
            'diagram', 'chart', 'graph', 'drawing', 'artwork', 'media', 'visual',
            'svg', 'canvas', 'bitmap', 'vector', 'icon', 'logo', 'screenshot'
        }
        
        return tag_name.lower() in graphics_elements
        
    def _get_element_text_content(self, element: etree.Element) -> str:
        """
        Extract text content from an element, preserving inner XML structure.
        
        Args:
            element: The XML element
            
        Returns:
            str: The text content with preserved XML structure
        """
        # For elements with child elements, get the complete inner XML
        if len(element) > 0:
            # Use tostring to get the complete content including child elements
            content = etree.tostring(element, encoding='unicode', method='xml')
            
            # Extract just the inner content (remove the outer element tags)
            tag_name = element.tag
            if tag_name.startswith('{'):
                # Handle namespaced tags - get namespace and local name
                ns_url, local_name = tag_name[1:].split('}')
                # Create pattern that matches both with and without namespace
                start_pattern = rf'<{re.escape(local_name)}[^>]*>|<[^:]+:{re.escape(local_name)}[^>]*>'
                end_pattern = rf'</{re.escape(local_name)}>|</[^:]+:{re.escape(local_name)}>'
            else:
                start_pattern = rf'<{re.escape(tag_name)}[^>]*>'
                end_pattern = rf'</{re.escape(tag_name)}>'
            
            # Remove opening tag
            content = re.sub(start_pattern, '', content, count=1)
            # Remove closing tag from the end
            content = re.sub(end_pattern + r'$', '', content)
            
            return content.strip()
        
        # For leaf elements, just return the text content
        return element.text or ""
        
    def _set_element_text_content(self, element: etree.Element, new_content: str):
        """
        Set the text content of an element, preserving inner XML structure and ALL attributes.
        CRITICAL: This method must preserve the exact structure and attributes as required by CMS validation.
        
        Args:
            element: The XML element
            new_content: The new content to set
        """
        # CRITICAL: Preserve ALL original attributes before modifying content
        original_attributes = dict(element.attrib)
        original_tail = element.tail
        
        # Clear existing content but preserve attributes
        element.clear()
        element.text = None
        element.tail = original_tail  # Preserve tail text
        
        # Restore ALL original attributes exactly as they were
        for attr_name, attr_value in original_attributes.items():
            element.set(attr_name, attr_value)
        
        # Parse and set new content
        if new_content.strip():
            try:
                # Wrap content in a temporary element to parse it
                temp_xml = f"<temp>{new_content}</temp>"
                temp_element = etree.fromstring(temp_xml)
                
                # Copy text and children from temp element
                element.text = temp_element.text
                for child in temp_element:
                    element.append(child)
                    
            except etree.XMLSyntaxError:
                # If content is not valid XML, treat as plain text
                element.text = new_content
                
        # VERIFICATION: Ensure all original attributes are still present
        for attr_name, attr_value in original_attributes.items():
            if element.get(attr_name) != attr_value:
                logger.warning(f"Attribute {attr_name} was modified! Restoring original value: {attr_value}")
                element.set(attr_name, attr_value)
                
    def _process_element(self, element: etree.Element, source: str, target: str, mode: Mode) -> bool:
        """
        Process a single XML element for translation.
        Only the direct text content (not markup or children) is sent to the translation API.
        CRITICAL: Must preserve all structure, attributes, and attribute values exactly.
        Graphics elements must never be modified in any way.
        """
        element_tag = getattr(element, 'tag', None)
        if not isinstance(element_tag, str):
            logger.error(f"Element has invalid tag: {element_tag} (type: {type(element_tag)})")
            return False
        if element_tag.startswith('{'):
            element_tag = element_tag.split('}')[1]
        logger.debug(f"Processing element: {element_tag}")
        # Graphics elements are always preserved completely unchanged for CMS compliance
        if self._is_graphics_element(element):
            logger.debug(f"Graphics element '{element_tag}' - preserving completely unchanged (CMS compliance mode)")
            self.preserved_elements += 1
            return True
        # Check if this element should be translated
        if not self._should_translate_element(element):
            tstatus = element.get('{http://www.simonsoft.se/ns/cms}tstatus', '') or element.get('cms:tstatus', '')
            if tstatus == TranslationStatus.RELEASED.value:
                self.preserved_elements += 1
                logger.debug(f"Preserved released element: {element_tag} (cms:tstatus=Released)")
                return True
            else:
                self.skipped_elements += 1
                logger.debug(f"Skipped element due to translation exclusion: {element_tag}")
                return False
        # Only translate the direct text (not markup or children)
        text_content = element.text or ""
        if not text_content.strip():
            logger.debug(f"Element has no direct text content, skipping: {element_tag}")
            return True
        try:
            logger.debug(f"Translating direct text: {text_content[:100]}..." if len(text_content) > 100 else f"Translating direct text: {text_content}")
            translated_result = self.translator.translate(
                {"text": text_content},
                source,
                [target],
                mode=mode,
                text_type=TextType.PLAIN
            )
            self._increment_and_log_translate_calls()
            translated_content = translated_result["text"][target]
            logger.debug(f"Translation result: {translated_content[:100]}..." if len(translated_content) > 100 else f"Translation result: {translated_content}")
            # Set only the direct text content, preserve children and tail
            element.text = translated_content
            self.processed_elements += 1
            logger.debug(f"Successfully translated element: {element_tag}")
            return True
        except Exception as e:
            logger.error(f"Error translating element {element_tag}: {type(e).__name__}: {str(e)}")
            import traceback
            logger.debug(f"Traceback: {traceback.format_exc()}")
            return False
            
    def _process_element_tree(self, root: etree.Element, source: str, target: str, mode: Mode):
        """
        Recursively process an element tree for translation.
        
        Args:
            root: The root element of the tree
            source: Source language code
            target: Target language code
            mode: Translation mode
        """
        # Process current element
        self._process_element(root, source, target, mode)
        
        # Recursively process child elements
        for child in root:
            self._process_element_tree(child, source, target, mode)
            
    def translate_coti_xml_file(self, 
                               xml_file_path: str, 
                               source: str, 
                               target: str, 
                               output_path: str = None,
                               mode: Mode = Mode.STANDARD_TURBO) -> str:
        """
        Translate a COTI XML file.
        
        Args:
            xml_file_path: Path to the input XML file
            source: Source language code
            target: Target language code
            output_path: Path for the output file (optional)
            mode: Translation mode
                            # Avoid infinite loops: skip if already processed
    # (Removed stray code here)
            
        Returns:
            str: Path to the translated XML file
        """
        self._reset_counters()
        
        logger.info(f"Starting COTI XML translation: {xml_file_path}")
        logger.info(f"Source: {source}, Target: {target}, Mode: {mode}")
        
        try:
            # Parse the XML file
            tree = etree.parse(xml_file_path)
            root = tree.getroot()
            logger.info(f"Successfully parsed XML file")
            
            # Check and log document attributes
            xml_lang = root.get('{http://www.w3.org/XML/1998/namespace}lang', '') or root.get('xml:lang', '')
            cms_rid = root.get('{http://www.simonsoft.se/ns/cms}rid', '') or root.get('cms:rid', '')
            cms_translation_project = root.get('{http://www.simonsoft.se/ns/cms}translation-project', '') or root.get('cms:translation-project', '')
            
            logger.info(f"Document attributes - xml:lang: {xml_lang}, cms:rid: {cms_rid}, cms:translation-project: {cms_translation_project}")
            
            # CRITICAL: DO NOT modify any attributes including xml:lang on graphics elements
            # The CMS validates structure and attributes must remain unchanged
            # Only update xml:lang on the root document element if it exists and is not a graphics element
            if xml_lang and self._is_document_root_element(root):
                original_lang = xml_lang
                if '{http://www.w3.org/XML/1998/namespace}lang' in root.attrib:
                    root.set('{http://www.w3.org/XML/1998/namespace}lang', target)
                elif 'xml:lang' in root.attrib:
                    root.set('xml:lang', target)
                logger.info(f"Updated document xml:lang from '{original_lang}' to '{target}' (root element only)")
            else:
                logger.info(f"Preserving xml:lang attribute unchanged (required for CMS validation)")
            
            # Process the document tree - ONLY translate text content, preserve all structure and attributes
            self._process_element_tree(root, source, target, mode)
            
            # Generate output path if not provided
            if not output_path:
                base_name = os.path.splitext(xml_file_path)[0]
                output_path = f"{base_name}_{source}-{target}.xml"
                
            # Save the translated XML
            tree.write(
                output_path,
                encoding='utf-8',
                xml_declaration=True,
                pretty_print=True
            )
            
            logger.info(f"Translation completed successfully!")
            logger.info(f"Statistics:")
            logger.info(f"  - Elements processed (translated): {self.processed_elements}")
            logger.info(f"  - Elements preserved (released): {self.preserved_elements}")
            logger.info(f"  - Elements skipped (excluded): {self.skipped_elements}")
            logger.info(f"  - Total translation calls: {self.translate_calls}")
            logger.info(f"Output saved to: {output_path}")
            
            return output_path
            
        except Exception as e:
            logger.error(f"Error during COTI XML translation: {e}")
            raise
            
    def translate(self,
                  texts: Dict[str, str],
                  source: str,
                  targets: List[str],
                  mode: Mode = Mode.STANDARD_TURBO) -> Dict[str, Dict[str, str]]:
        """
        Translate COTI XML content from a dictionary of texts.
        
        Args:
            texts: Dictionary of text content to translate
            source: Source language code
            targets: List of target language codes
            mode: Translation mode
            
        Returns:
            Dictionary with translated content for each target language
            Format: {text_id: {language: translated_content}}
        """
        self._reset_counters()
        
        logger.info(f"Starting COTI XML translation from dictionary")
        logger.info(f"Source: {source}, Targets: {targets}, Mode: {mode}")
        logger.info(f"Number of texts to translate: {len(texts)}")
        
        results = {}
        
        for text_id, xml_content in texts.items():
            logger.info(f"Processing text ID: {text_id}")
            # Sanitize XML and store invalid chars and ampersands
            sanitized_xml, invalids, amp_positions = self._sanitize_xml(xml_content)
            try:
                # Parse XML content
                root = etree.fromstring(sanitized_xml.encode('utf-8'))
                logger.debug(f"Successfully parsed XML content for text ID: {text_id}")
                # Create translations for each target language
                translations = {}
                for target in targets:
                    logger.info(f"Translating to {target}")
                    # Create a copy of the root for this target language
                    target_root = copy.deepcopy(root)
                    # Update xml:lang if it exists
                    xml_lang = target_root.get('{http://www.w3.org/XML/1998/namespace}lang', '') or target_root.get('xml:lang', '')
                    if xml_lang:
                        if '{http://www.w3.org/XML/1998/namespace}lang' in target_root.attrib:
                            target_root.set('{http://www.w3.org/XML/1998/namespace}lang', target)
                        elif 'xml:lang' in target_root.attrib:
                            target_root.set('xml:lang', target)
                    # Process the element tree
                    self._process_element_tree(target_root, source, target, mode)
                    # Convert back to string
                    translated_xml = etree.tostring(target_root, encoding='unicode', pretty_print=True)
                    # Restore invalid chars and ampersands
                    translated_xml = self._restore_invalid_chars(translated_xml, invalids, amp_positions)
                    translations[target] = translated_xml
                    logger.info(f"Completed translation to {target}")
                results[text_id] = translations
            except Exception as e:
                import traceback
                logger.error(f"Error processing text ID {text_id}: {e}\n{traceback.format_exc()}")
                # Return original content for all targets as fallback
                results[text_id] = {target: xml_content for target in targets}
        
        logger.info(f"COTI XML translation completed!")
        logger.info(f"Statistics:")
        logger.info(f"  - Elements processed (translated): {self.processed_elements}")
        logger.info(f"  - Elements preserved (released): {self.preserved_elements}")
        logger.info(f"  - Elements skipped (excluded): {self.skipped_elements}")
        logger.info(f"  - Total translation calls: {self.translate_calls}")
        
        return results
        
    def validate_coti_structure(self, xml_file_path: str) -> Dict[str, Any]:
        """
        Validate the structure of a COTI XML file and return analysis.
        
        Args:
            xml_file_path: Path to the XML file to validate
            
        Returns:
            Dictionary with validation results and statistics
        """
        logger.info(f"Validating COTI XML structure: {xml_file_path}")
        
        try:
            tree = etree.parse(xml_file_path)
            root = tree.getroot()
            
            # Extract document metadata
            xml_lang = root.get('{http://www.w3.org/XML/1998/namespace}lang', '') or root.get('xml:lang', '')
            cms_rid = root.get('{http://www.simonsoft.se/ns/cms}rid', '') or root.get('cms:rid', '')
            cms_translation_project = root.get('{http://www.simonsoft.se/ns/cms}translation-project', '') or root.get('cms:translation-project', '')
            
            # Count elements by translation status
            status_counts = {}
            exclusion_counts = 0
            total_elements = 0
            
            def count_elements(element):
                nonlocal total_elements, exclusion_counts
                total_elements += 1
                
                # Check translation status
                tstatus = element.get('{http://www.simonsoft.se/ns/cms}tstatus', '') or element.get('cms:tstatus', '')
                if tstatus:
                    status_counts[tstatus] = status_counts.get(tstatus, 0) + 1
                    
                # Check translation exclusions
                translate_attr = element.get('translate', '').lower()
                markfortrans_attr = element.get('markfortrans', '').lower()
                if translate_attr == 'no' or markfortrans_attr == 'no':
                    exclusion_counts += 1
                    
                # Recursively count children
                for child in element:
                    count_elements(child)
                    
            count_elements(root)
            
            validation_result = {
                'valid': True,
                'file_path': xml_file_path,
                'document_metadata': {
                    'xml_lang': xml_lang,
                    'cms_rid': cms_rid,
                    'cms_translation_project': cms_translation_project
                },
                'statistics': {
                    'total_elements': total_elements,
                    'translation_status_counts': status_counts,
                    'translation_exclusions': exclusion_counts,
                    'translatable_elements': total_elements - status_counts.get(TranslationStatus.RELEASED.value, 0) - exclusion_counts
                }
            }
            
            logger.info(f"COTI XML validation completed successfully")
            logger.info(f"Document language: {xml_lang}")
            logger.info(f"Translation project: {cms_translation_project}")
            logger.info(f"Total elements: {total_elements}")
            logger.info(f"Released elements: {status_counts.get(TranslationStatus.RELEASED.value, 0)}")
            logger.info(f"Excluded elements: {exclusion_counts}")
            logger.info(f"Translatable elements: {validation_result['statistics']['translatable_elements']}")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating COTI XML structure: {e}")
            return {
                'valid': False,
                'error': str(e),
                'file_path': xml_file_path
            }


if __name__ == "__main__":
    try:
        # Example usage
        translator = COTIXMLTranslator(verify=False)
        # Example 1: Validate a COTI XML file structure
        test_folder = os.path.join(os.path.dirname(__file__), "..", "test")
        coti_file = os.path.join(test_folder, "coti_sample.xml")
        
        # Create a sample COTI XML file for testing
        sample_coti_xml = '''<?xml version="1.0" encoding="UTF-8"?>
<document xml:lang="en" cms:rid="doc123" cms:translation-project="proj456" xmlns:cms="http://www.simonsoft.se/ns/cms">
        
        except Exception as e:
            logger.error(f"Unhandled exception in __main__: {e}")
        <heading translate="no">Technical Specifications</heading>
        <content cms:tstatus="In_Translation">
            <p>This product offers excellent performance and reliability.</p>
            <p cms:tstatus="Released">Model: XYZ-2000</p>
            <p>Available in multiple configurations to meet your needs.</p>
        </content>
    </section>
    <section cms:rid="sec2" markfortrans="no">
        <heading>Internal Notes</heading>
        <content>
            <p>These are internal notes not for translation.</p>
        </content>
    </section>
    <section cms:rid="sec3">
        <heading>Features</heading>
        <content>
            <p>Advanced technology for optimal results.</p>
            <p>Easy to use interface.</p>
            <p cms:tstatus="Review">Energy efficient operation.</p>
        </content>
    </section>
</document>'''
        
        # Save sample file
        with open(coti_file, 'w', encoding='utf-8') as f:
            f.write(sample_coti_xml)
        
        print("Created sample COTI XML file")
        
        # Validate the structure
        validation_result = translator.validate_coti_structure(coti_file)
        print(f"Validation result: {validation_result}")
        
        # Translate the file
        if validation_result['valid']:
            output_file = translator.translate_coti_xml_file(
                coti_file, 
                source="en", 
                target="de",
                mode=Mode.STANDARD_TURBO
            )
            print(f"Translation completed! Output: {output_file}")
            
            # Read and display the result
            with open(output_file, 'r', encoding='utf-8') as f:
                translated_content = f.read()
            print(f"Translated content (first 500 chars):\n{translated_content[:500]}...")
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        print(f"Error: {e}")
