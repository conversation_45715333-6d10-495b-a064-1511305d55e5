#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to verify that batch proofreading is actually working.
This script will show you exactly what's happening in the logs.
"""

import sys
import os
import logging
import time
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.simonsoft_xml import SimonsoftXMLTranslator
from utils.core import Mode

# Configure logging to show everything
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),  # Print to console
        logging.FileHandler('batch_verification.log')  # Also save to file
    ]
)

def create_test_xml():
    """Create a simple test XML with multiple translatable elements."""
    return """<?xml version="1.0" encoding="UTF-8"?>
<document>
    <title>Test Document</title>
    <section>
        <p>This is the first paragraph to translate.</p>
        <p>This is the second paragraph to translate.</p>
        <p>This is the third paragraph to translate.</p>
        <p>This is the fourth paragraph to translate.</p>
        <p>This is the fifth paragraph to translate.</p>
    </section>
    <section>
        <p>Another section with more text.</p>
        <p>Even more text to translate here.</p>
        <p>And yet another paragraph.</p>
        <p>One more paragraph for good measure.</p>
        <p>Final paragraph in this test.</p>
    </section>
</document>"""

def test_batch_vs_individual():
    """Compare batch vs individual processing and show the difference in logs."""
    
    print("=" * 60)
    print("BATCH PROCESSING VERIFICATION TEST")
    print("=" * 60)
    
    xml_content = create_test_xml()
    translator = SimonsoftXMLTranslator(verify=False)  # Use verify=False for testing
    
    print(f"\nTest XML has multiple <p> tags to translate")
    print(f"Testing with small batch_size=3 to see multiple batches\n")
    
    # Test 1: Batch Mode
    print("🔍 TEST 1: BATCH PROOFREADING MODE")
    print("-" * 40)
    
    start_time = time.time()
    
    try:
        results_batch = translator.translate_xml(
            xml_content,
            source_lang="en",
            target_langs=["de"],
            mode=Mode.STANDARD_PROOFREAD_BATCH,
            batch_size=3  # Small batch size to see multiple batches
        )
        
        batch_time = time.time() - start_time
        print(f"✅ Batch mode completed in {batch_time:.2f} seconds")
        print(f"📊 Total API calls made: {translator.translate_calls}")
        
    except Exception as e:
        print(f"❌ Batch mode failed: {e}")
        return False
    
    # Reset counter for next test
    translator.translate_calls = 0
    
    print("\n" + "="*60)
    
    # Test 2: Individual Mode (for comparison)
    print("🔍 TEST 2: INDIVIDUAL PROOFREADING MODE")
    print("-" * 40)
    
    start_time = time.time()
    
    try:
        results_individual = translator.translate_xml(
            xml_content,
            source_lang="en", 
            target_langs=["de"],
            mode=Mode.STANDARD_PROOFREAD,  # Individual mode
            batch_size=3
        )
        
        individual_time = time.time() - start_time
        print(f"✅ Individual mode completed in {individual_time:.2f} seconds")
        print(f"📊 Total API calls made: {translator.translate_calls}")
        
    except Exception as e:
        print(f"❌ Individual mode failed: {e}")
        return False
    
    print("\n" + "="*60)
    print("📈 PERFORMANCE COMPARISON")
    print("-" * 40)
    print(f"Batch mode time:      {batch_time:.2f}s")
    print(f"Individual mode time: {individual_time:.2f}s")
    
    if batch_time < individual_time:
        speedup = individual_time / batch_time
        print(f"🚀 Batch mode is {speedup:.1f}x faster!")
    else:
        print("⚠️  Batch mode didn't show expected speedup (might be due to small test size)")
    
    return True

def monitor_logs_realtime():
    """Show how to monitor logs in real-time."""
    print("\n" + "="*60)
    print("📋 HOW TO MONITOR LOGS IN REAL-TIME")
    print("="*60)
    
    print("\n🪟 Windows PowerShell:")
    print("Get-Content app.log -Wait -Tail 20")
    print("Get-Content logs/app.log -Wait -Tail 20")
    
    print("\n🪟 Windows Command Prompt:")
    print('type app.log | findstr "batch proofreading"')
    print('type app.log | findstr "Starting batch"')
    print('type app.log | findstr "API call"')
    
    print("\n🐧 Linux/Mac:")
    print("tail -f app.log | grep -i batch")
    print("tail -f logs/app.log | grep -i batch")

def show_log_patterns():
    """Show what to look for in the logs."""
    print("\n" + "="*60)
    print("🔍 WHAT TO LOOK FOR IN THE LOGS")
    print("="*60)
    
    print("\n✅ BATCH PROCESSING (what you SHOULD see):")
    print("   Starting batch proofreading for X texts")
    print("   Batch proofreading completed successfully")
    print("   [PROGRESS] Finished batch 1/N (API call 1, nodes 1-X)")
    
    print("\n❌ INDIVIDUAL PROCESSING (fallback):")
    print("   Batch proofreader failed, falling back to individual processing")
    print("   Individual proofreader failed")
    print("   Multiple 'Proofreader completed' messages")
    
    print("\n📊 PERFORMANCE INDICATORS:")
    print("   Lower number of 'API call' mentions = better batching")
    print("   'Starting batch proofreading for X texts' where X > 1")

if __name__ == "__main__":
    print("🧪 Batch Processing Verification Tool")
    print("This tool will help you verify that batch proofreading is working correctly.\n")
    
    # Show what to look for
    show_log_patterns()
    
    # Show how to monitor logs
    monitor_logs_realtime()
    
    # Ask user if they want to run the test
    print("\n" + "="*60)
    response = input("Do you want to run the verification test? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        print("\n🚀 Starting verification test...")
        print("📝 Watch the console output and check the logs!")
        print("📁 Logs are also saved to: batch_verification.log\n")
        
        success = test_batch_vs_individual()
        
        if success:
            print("\n🎉 Verification test completed!")
            print("📁 Check 'batch_verification.log' for detailed logs")
        else:
            print("\n❌ Verification test failed!")
            
    else:
        print("\n👋 Test skipped. Use the log monitoring commands above to check manually.")
