# Contributing to Babel Service

## Development Workflow

1. **Create a feature branch** from the main branch
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes** and commit them with clear, descriptive commit messages
   ```bash
   git commit -m "Add feature: description of your changes"
   ```

3. **Push your branch** to your fork
   ```bash
   git push origin feature/your-feature-name
   ```

4. **Create a pull request** from your branch to the **dev** repository


## Testing

TODO

## Documentation

- Update documentation for any changes to the API or functionality
- Add docstrings to all new functions, classes, and modules
- Update the README.md if necessary
- Consider adding examples for complex features

## Submitting Changes

1. **Ensure your code passes all tests**
2. **Update documentation** as needed
3. **Create a pull request** with a clear description of your changes
4. **Address any feedback** from code reviews

## Managing Dependencies

Dependencies are managed using `pip-tools`:

1. Add a dependency to `requirements.in`
2. Run `pip-compile requirements.in` to update `requirements.txt`
3. Run `pip-sync` to install the updated dependencies

> Do not modify `requirements.txt` directly as it is automatically managed by pip-tools.

Example:

```bash
# Add a new dependency
echo "requests>=2.25.0" >> requirements.in

# Update requirements.txt
pip-compile requirements.in

# Install dependencies
pip-sync
```

## Adding Support for New Tags

To add support for a new tag type:

1. Add an entry to the `dict.json` file:
   ```json
   "my_custom_tag": {
       "translatable": true,
       "self_closing": false,
       "attributes": {
           "title": {
               "translatable": true,
               "encoding": false,
               "delimited_encoding": false,
               "not_encoding": true,
               "notes": "",
               "attributes": {}
           }
       }
   }
   ```

2. If the tag requires special handling, update the relevant code in `xml.py`

3. Add tests for the new tag in the `test` directory

## Thank You!

Your contributions help make this project better for everyone. We appreciate your time and effort!
