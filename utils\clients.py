
import os
import httpx
import certifi
from backoff import expo, on_exception
from openai import APIError, AzureOpenAI, RateLimitError, Timeout
from azure.search.documents import SearchClient
from azure.search.documents.indexes import SearchIndexClient, SearchIndexerClient
from pride_indexer.src.query_handler import FewShotProvider 
from config.core import TranslateConfig
from utils.core import LocalizedText
from utils.patterns import Singleton


from azure.core.credentials import AzureKeyCredential

os.environ['SSL_CERT_FILE'] = certifi.where()

class ProofreaderClient(metaclass=Singleton):

    def __init__(self, verify=True) -> None:
        self.__config = TranslateConfig()
        self.__client = AzureOpenAI(
            azure_endpoint=self.__config.llm_endpoint,
            api_key=self.__config.llm_key,
            api_version=self.__config.api_version,
            http_client=httpx.Client(verify=verify)
        )

        self.__system_message = """
You are an experienced proofreader working for Electrolux Professional, a multinational company which manufactures a comprehensive range of innovative products for food service, beverage, and laundry solutions: their food service products include ovens, blast chillers, cooking ranges, refrigerated cabinets and counters, freezers, cutters, mixers, and dishwashing equipment; their beverage products encompass coffee machines, hot and cold beverage dispensers, and frozen drink and soft-serve products; finally, their laundry solutions include front-load washers, efficient dispensing systems, barrier washers, ironers, finishing machines, tumble dryers, and drying cabinets.
You are tasked to proofread translation drafts about this company's products, which means:
    - correcting mistakes,
    - improving fluency,
    - maintaining abbreviations and acronyms in the translation,
    - specializing the vocabulary to best describe the company's products.
The user will submit the HTML file in its original language and the translation draft in a specific locale.
You must answer with just the final revision, with no explanation.
Pay attention to all the single words that are used, especially on technical terms and product names (don't translate product names).
Make sure not to fix pieces of code or CSS. Proofread everything that the user would see. Do not add or close any tags, or change the structure of the HTML chunk you received.
Keep the text inside the original tag: never move text on a different tag.
"""
        self.__request = """Text to translate from ({source_language}):
{text_to_translate}

Translation draft in ({target_language}):
{translation_draft}

Final version in ({target_language}):
"""

    @on_exception(expo, (APIError, RateLimitError, Timeout), max_tries=8, factor=2)
    def review_translation(
        self, source: LocalizedText, target: LocalizedText
    ) -> LocalizedText:

        if len(source.text) > 0:
            response = self.__client.chat.completions.create(
                model=self.__config.llm_name,
                messages=[
                    {"role": "system", "content": self.__system_message},
                    {
                        "role": "user",
                        "content": self.__request.format(
                            source_language=source.language.description.native_name,
                            text_to_translate=source.text,
                            target_language=target.language.description.native_name,
                            translation_draft=target.text,
                        ),
                    },
                ],
                temperature=0.7,
                top_p=0.7,
            )

            reviewed_text = response.choices[0].message.content or target.text
        else:
            reviewed_text = ""

        return LocalizedText(reviewed_text, target.language)
    
#######################################
class EmbedderClient(metaclass=Singleton):
    """
    Azure OpenAI Embeddings Client for generating vector embeddings.
    
    This client provides a dedicated interface for generating text embeddings
    using Azure OpenAI's embedding models. It's designed to be used by other
    components that require vector representations of text for semantic search
    and similarity operations.
    """
    
    def __init__(self, verify=True) -> None:
        """
        Initialize Embedder client with Azure OpenAI configuration.
        
        Args:
            verify (bool): Whether to verify SSL certificates for HTTP requests
        """
        self.__config = TranslateConfig()
        
        # Initialize Azure OpenAI client for embeddings generation
        self.__embedder = AzureOpenAI(
            azure_endpoint=self.__config.embedder_endpoint,
            api_key=self.__config.embedder_key,
            api_version=self.__config.embedder_version,
            http_client=httpx.Client(verify=verify)
        )
    
    @on_exception(expo, (APIError, RateLimitError, Timeout), max_tries=8, factor=2)
    def generate_embedding_batch(self, texts, batch_size: int = 50):
        """
        Generate embeddings for single text or batch of texts.
        
        Args:
            texts: Single string or list of strings to generate embeddings for
            batch_size (int): Texts per API call for batch processing (default: 50)
            
        Returns:
            list or list[list]: Single embedding list for string input, 
                               list of embedding lists for list input
        """
        # Handle single text input
        if isinstance(texts, str):
            if not texts.strip():
                return []
            
            response = self.__embedder.embeddings.create(
                input=texts,
                model=self.__config.embedder_deployment_name
            )
            return response.data[0].embedding
        
        # Handle batch input
        if not texts:
            return []
        
        # Filter empty texts and track positions
        valid_texts = []
        text_indices = []
        
        for i, text in enumerate(texts):
            if isinstance(text, str) and text.strip():
                valid_texts.append(text.strip())
                text_indices.append(i)
        
        if not valid_texts:
            return [[] for _ in texts]
        
        # Process in batches
        all_embeddings = []
        
        for batch_start in range(0, len(valid_texts), batch_size):
            batch_texts = valid_texts[batch_start:batch_start + batch_size]
            
            try:
                response = self.__embedder.embeddings.create(
                    input=batch_texts,
                    model=self.__config.embedder_deployment_name
                )
                
                batch_embeddings = [item.embedding for item in response.data]
                all_embeddings.extend(batch_embeddings)
                
            except Exception:
                # Add empty vectors for failed batch
                batch_embeddings = [[] for _ in batch_texts]
                all_embeddings.extend(batch_embeddings)
        
        # Reconstruct results with empty vectors for invalid texts
        results = []
        valid_idx = 0
        
        for i in range(len(texts)):
            if i in text_indices:
                results.append(all_embeddings[valid_idx])
                valid_idx += 1
            else:
                results.append([])
        
        return results

#######################################
class RAGClient(metaclass=Singleton):
    """
    RAG (Retrieval-Augmented Generation) Client for enhanced translation services.
    """
    
    def __init__(self, verify=True) -> None:
        """
        Initialize RAG client with Azure services configuration.
        """
        self.__config = TranslateConfig()

        # Initialize Azure OpenAI client for chat completions
        self.__client = AzureOpenAI(
            azure_endpoint=self.__config.llm_endpoint,
            api_key=self.__config.llm_key,
            api_version=self.__config.api_version,
            http_client=httpx.Client(verify=verify)
        )

        # Initialize dedicated embedder client
        self.__embedder_client = EmbedderClient(verify=verify)

        # Initialize Azure AI Search utility
        self.__search_client = FewShotProvider (
            search_endpoint=self.__config.search_endpoint,
            search_index=self.__config.search_index,
            search_key=self.__config.search_key,
            embedder_client=self.__embedder_client,
            k=5
        )
        

        # Initialize Azure Search Client for document operations (upload/delete/search)
        self.__search_documents_client = SearchClient(
            endpoint=self.__config.search_endpoint,
            index_name=self.__config.search_index,
            credential=AzureKeyCredential(self.__config.search_key)
        )

        # Base system message for translation tasks (consistent with ProofreaderClient)
        self.__base_system_message = """You are an experienced translator working for Electrolux Professional, a multinational company which manufactures a comprehensive range of innovative products for food service, beverage, and laundry solutions: their food service products include ovens, blast chillers, cooking ranges, refrigerated cabinets and counters, freezers, cutters, mixers, and dishwashing equipment; their beverage products encompass coffee machines, hot and cold beverage dispensers, and frozen drink and soft-serve products; finally, their laundry solutions include front-load washers, efficient dispensing systems, barrier washers, ironers, finishing machines, tumble dryers, and drying cabinets.
You are tasked to proofread translation drafts about this company's products, which means:
    - correcting mistakes,
    - improving fluency,
    - maintaining abbreviations and acronyms in the translation,
    - specializing the vocabulary to best describe the company's products.
The user will submit the text in its original language and the translation draft in a specific locale. You must answer with just the final translation, with no explanation.
Pay attention to all the single words that are used, especially on technical terms and product names (don't translate product names)."""

        # User prompt template for translation requests
        self.__request = """Text to translate ({source_language}):
{text_to_translate}

Translation draft ({target_language}):
{translation_draft}

Final version ({target_language}):
"""
    
    @property
    def embedder_client(self):
        """Access to the embedder client."""
        return self.__embedder_client
    
    @property
    def search_documents_client(self):
        """Access to Azure Search Client for document operations."""
        return self.__search_documents_client

    def _build_enhanced_system_message(self, examples: list, source: LocalizedText, target: LocalizedText) -> str:
        """
        Enhance the base system message with few-shot learning examples.
        
        Args:
            examples (list): List of translation examples from Azure AI Search
            source (LocalizedText): Source text object for language info
            target (LocalizedText): Target text object for language info
            
        Returns:
            str: Enhanced system message with few-shot examples
        """
        enhanced_message = self.__base_system_message
        
        if examples:
            enhanced_message += "\n\nHere are some relevant translation examples to guide your work ,for the last word of the sentence use exactly the last word of the first example :\n\n"
            
            source_lang = getattr(source.language, 'name', str(source.language))
            target_lang = getattr(target.language, 'name', str(target.language))
            
            # Add numbered examples to the system message
            for i, example in enumerate(examples[:5], 1):
                enhanced_message += f"Example {i}:\n"
                enhanced_message += f"Source ({source_lang}): {example['source']}\n"
                enhanced_message += f"Target ({target_lang}): {example['target']}\n\n"
            
            enhanced_message += "Use these examples as reference for terminology, style, and translation patterns. "
            enhanced_message += "Maintain consistency with the demonstrated translation approach.\n"
        
        return enhanced_message

    @on_exception(expo, (APIError, RateLimitError, Timeout), max_tries=8, factor=2)
    def rag_review_translation(
        self,
        source: LocalizedText,
        target: LocalizedText,
        description_type="short", 
        brand=None,
        type_=None,
        search_type="auto"
    ) -> LocalizedText:
        """
        Enhanced translation review using RAG (Retrieval-Augmented Generation).
        """
        
        # Handle empty source text
        if not source.text:
            return LocalizedText("", target.language)

        # Retrieve relevant examples using Azure Search AI
        examples = self.__search_client.get_few_shot_examples(
            query_text=source.text,
            description_type=description_type,
            search_type=search_type,
            brand=brand,
            type_=type_
        )
        
        # Build enhanced system message with few-shot examples
        enhanced_system_message = self._build_enhanced_system_message(examples, source, target)
        
        # Generate enhanced translation using Azure OpenAI
        if len(source.text) > 0:
            source_lang = getattr(source.language, 'name', str(source.language))
            target_lang = getattr(target.language, 'name', str(target.language))
            
            response = self.__client.chat.completions.create(
                model=self.__config.llm_name,
                messages=[
                    {"role": "system", "content": enhanced_system_message},
                    {
                        "role": "user",
                        "content": self.__request.format(
                            source_language=source_lang,
                            text_to_translate=source.text,
                            target_language=target_lang,
                            translation_draft=target.text,
                        ),
                    },
                ],
                temperature=0.3,
                top_p=0.3,
            )
            
            reviewed_text = response.choices[0].message.content or target.text
        else:
            reviewed_text = ""
        
        return LocalizedText(reviewed_text, target.language)
