"""
XML Section Tree Visualizer

This script creates a visual tree representation of section tags in XML files,
showing the hierarchical structure with proper indentation and section information.
"""

import os
import sys
from lxml import etree
from typing import Dict, List, Optional
import argparse

def find_section_end_line(element: etree.Element, xml_content_lines: List[str]) -> int:
    """
    Find the end line of a section by properly matching opening and closing tags.
    Uses a stack-based approach to handle nested sections correctly.
    """
    start_line = getattr(element, 'sourceline', None)
    if start_line is None:
        return 'Unknown'
    
    # Count nested sections to find the matching closing tag
    section_depth = 0
    found_start = False
    
    # Start from the line where this section opens
    for i in range(start_line - 1, len(xml_content_lines)):
        line = xml_content_lines[i].strip()
        
        # Look for section tags (both self-closing and regular)
        if '<section' in line and not line.startswith('<!--'):
            if line.endswith('/>'):
                # Self-closing section tag, skip it
                continue
            elif '>' in line:
                # Opening section tag
                if i == start_line - 1:
                    # This is our starting section
                    found_start = True
                    section_depth = 1
                elif found_start:
                    # Nested section
                    section_depth += 1
        
        elif line == '</section>' and found_start:
            # Closing section tag
            section_depth -= 1
            if section_depth == 0:
                # Found our matching closing tag
                return i + 1
    
    return 'Unknown'

def extract_section_info(element: etree.Element, xml_lines: List[str] = None) -> Dict[str, str]:
    """Extract relevant information from a section element."""
    info = {
        'cms_rid': element.get('{http://www.simonsoft.se/namespace/cms}rid') or element.get('cms:rid', 'No RID'),
        'xml_id': element.get('{http://www.w3.org/XML/1998/namespace}id') or element.get('xml:id', ''),
        'type': element.get('type', ''),
        'newpage': element.get('newpage', ''),
        'pagesetup': element.get('pagesetup', ''),
        'status': element.get('status', ''),
        'modifiedby': element.get('modifiedby', ''),
        'modifieddate': element.get('modifieddate', '')
    }
    
    # Try to get title from first title element
    title_elem = element.find('.//title')
    if title_elem is not None and title_elem.text:
        info['title'] = title_elem.text.strip()[:50] + ('...' if len(title_elem.text.strip()) > 50 else '')
    else:
        info['title'] = 'No title'
    
    # Calculate section length (character count and element count)
    section_text = etree.tostring(element, encoding='unicode', method='text')
    info['char_count'] = len(section_text.strip())
    info['element_count'] = len(list(element.iter())) - 1  # Subtract 1 to exclude the section element itself
    
    # Get line numbers
    info['start_line'] = getattr(element, 'sourceline', 'Unknown')
    info['end_line'] = find_section_end_line(element, xml_lines) if xml_lines else 'Unknown'
    
    return info

def build_section_tree(root: etree.Element, xml_lines: List[str] = None) -> List[Dict]:
    """Build a tree structure of sections with their hierarchy."""
    def traverse_sections(element: etree.Element, level: int = 0) -> List[Dict]:
        sections = []
        
        if element.tag == 'section':
            info = extract_section_info(element, xml_lines)
            info['level'] = level
            info['element'] = element
            sections.append(info)
            
            # Find direct child sections
            for child in element:
                if child.tag == 'section':
                    sections.extend(traverse_sections(child, level + 1))
                else:
                    # Look for sections in other child elements
                    for nested in child.iter('section'):
                        if nested.getparent() == child:  # Only direct children
                            sections.extend(traverse_sections(nested, level + 1))
        else:
            # Look for sections in child elements
            for child in element:
                sections.extend(traverse_sections(child, level))
                
        return sections
    
    return traverse_sections(root)

def print_tree_visualization(sections: List[Dict], show_attributes: bool = True):
    """Print a tree visualization of the sections."""
    print("XML Section Tree Structure")
    print("=" * 80)
    print()
    
    for section in sections:
        # Create indentation based on level
        indent = "│   " * section['level']
        if section['level'] > 0:
            indent = indent[:-4] + "├── "
        
        # Basic section info
        section_line = f"{indent}<section> {section['title']}"
        
        if section['xml_id']:
            section_line += f" [ID: {section['xml_id']}]"
        
        # Add length and line information
        if section['start_line'] != 'Unknown' and section['end_line'] != 'Unknown':
            line_info = f"Lines {section['start_line']}-{section['end_line']}"
        elif section['start_line'] != 'Unknown':
            line_info = f"Line {section['start_line']}"
        else:
            line_info = "Lines: Unknown"
        section_line += f" ({section['char_count']:,} chars, {section['element_count']} elements, {line_info})"
        
        print(section_line)
        
        if show_attributes:
            # Show important attributes with additional indentation
            attr_indent = "│   " * (section['level'] + 1)
            
            if section['type']:
                print(f"{attr_indent}• Type: {section['type']}")
            if section['newpage']:
                print(f"{attr_indent}• New Page: {section['newpage']}")
            if section['pagesetup']:
                print(f"{attr_indent}• Page Setup: {section['pagesetup']}")
            if section['status']:
                print(f"{attr_indent}• Status: {section['status']}")
            if section['modifiedby']:
                print(f"{attr_indent}• Modified By: {section['modifiedby']} ({section['modifieddate']})")
            
            print(f"{attr_indent}• CMS RID: {section['cms_rid']}")
            print()

def generate_summary_stats(sections: List[Dict]):
    """Generate summary statistics about the sections."""
    print("Section Statistics")
    print("=" * 40)
    print(f"Total sections: {len(sections)}")
    
    # Count by level
    level_counts = {}
    for section in sections:
        level = section['level']
        level_counts[level] = level_counts.get(level, 0) + 1
    
    print("\nSections by hierarchy level:")
    for level in sorted(level_counts.keys()):
        print(f"  Level {level}: {level_counts[level]} sections")
    
    # Count by type
    type_counts = {}
    for section in sections:
        section_type = section['type'] or 'No type'
        type_counts[section_type] = type_counts.get(section_type, 0) + 1
    
    print(f"\nSections by type:")
    for section_type, count in sorted(type_counts.items()):
        print(f"  {section_type}: {count}")
    
    # Count with IDs
    sections_with_ids = sum(1 for section in sections if section['xml_id'])
    print(f"\nSections with xml:id: {sections_with_ids}")
    
    # Length statistics
    total_chars = sum(section['char_count'] for section in sections)
    total_elements = sum(section['element_count'] for section in sections)
    avg_chars = total_chars / len(sections) if sections else 0
    avg_elements = total_elements / len(sections) if sections else 0
    
    print(f"\nContent statistics:")
    print(f"  Total characters: {total_chars:,}")
    print(f"  Total elements: {total_elements:,}")
    print(f"  Average characters per section: {avg_chars:,.0f}")
    print(f"  Average elements per section: {avg_elements:.1f}")
    
    # Find largest sections
    if sections:
        largest_by_chars = sorted(sections, key=lambda x: x['char_count'], reverse=True)[:5]
        largest_by_elements = sorted(sections, key=lambda x: x['element_count'], reverse=True)[:5]
        
        print(f"\nLargest sections by character count:")
        for i, section in enumerate(largest_by_chars, 1):
            print(f"  {i}. {section['title'][:40]}... ({section['char_count']:,} chars)")
        
        print(f"\nLargest sections by element count:")
        for i, section in enumerate(largest_by_elements, 1):
            print(f"  {i}. {section['title'][:40]}... ({section['element_count']} elements)")
    
    print()

def export_to_text_file(sections: List[Dict], output_file: str, show_attributes: bool = True):
    """Export the tree visualization to a text file."""
    with open(output_file, 'w', encoding='utf-8') as f:
        # Redirect print to file
        import sys
        old_stdout = sys.stdout
        sys.stdout = f
        
        print_tree_visualization(sections, show_attributes)
        generate_summary_stats(sections)
        
        # Restore stdout
        sys.stdout = old_stdout
    
    print(f"Tree visualization exported to: {output_file}")

def main():
    parser = argparse.ArgumentParser(description='Visualize XML section tree structure')
    parser.add_argument('xml_file', help='Path to the XML file')
    parser.add_argument('--no-attributes', action='store_true', help='Hide section attributes')
    parser.add_argument('--output', '-o', help='Output file path (optional)')
    parser.add_argument('--stats-only', action='store_true', help='Show only statistics')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.xml_file):
        print(f"Error: File {args.xml_file} not found")
        return 1
    
    try:
        # Parse XML with namespace handling and line number preservation
        parser = etree.XMLParser(recover=True, strip_cdata=False)
        tree = etree.parse(args.xml_file, parser)
        root = tree.getroot()
        
        print(f"Processing XML file: {args.xml_file}")
        print(f"Root element: <{root.tag}>")
        print()
        
        # Read XML content as lines for end line calculation
        with open(args.xml_file, 'r', encoding='utf-8') as f:
            xml_lines = f.readlines()
        
        # Build section tree
        sections = build_section_tree(root, xml_lines)
        
        if not sections:
            print("No section elements found in the XML file.")
            return 0
        
        if args.stats_only:
            generate_summary_stats(sections)
        else:
            print_tree_visualization(sections, not args.no_attributes)
            generate_summary_stats(sections)
        
        # Export to file if requested
        if args.output:
            export_to_text_file(sections, args.output, not args.no_attributes)
        
        return 0
        
    except etree.XMLSyntaxError as e:
        print(f"XML parsing error: {e}")
        return 1
    except Exception as e:
        print(f"Error: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
