"""
XML Translation Module

This module provides functionality for translating XML/HTML content while preserving
tags and handling special attribute encodings (JSON, pipe-delimited).
"""

# Standard library imports
import os
import re
import json
import copy
import urllib.parse
import logging
from typing import Dict, List, Optional, Union, Any, Tuple
from lxml import etree
from azure.ai.translation.text.models import TextType
from src.textual import TextTranslator
from utils.core import Language, Translator, Mode, get_logger
from utils.patterns import Singleton

# Configure logging
urllib3_logger = logging.getLogger('urllib3')
urllib3_logger.setLevel(logging.ERROR)
azure_logger = logging.getLogger('azure')
azure_logger.setLevel(logging.ERROR)

logger = get_logger(__file__)

# Constants
DICT_PATH = os.path.join(os.path.dirname(__file__), "dict.json")

HTML_VOID_ELEMENTS = {
    'area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input', 'link', 'meta', 'param', 'source', 'track', 'wbr'
}

# Load dictionary configuration
try:
    with open(DICT_PATH, "r", encoding="utf-8") as file:
        DICTIONARY = json.load(file)
except (FileNotFoundError, json.JSONDecodeError) as e:
    logger.error(f"Error loading dictionary file: {e}")
    DICTIONARY = {}


def is_attribute_to_translate(tag_name: str, attr_name: str, parent_chain: List[str] = None, original_tag: str = None) -> bool:
    """
    Checks if an attribute is directly translatable (so not encoded) by looking it up in the dictionary.
    Handles both top-level and nested attributes with full parent chain support.

    Args:
        tag_name: The name of the XML/HTML tag
        attr_name: The name of the attribute to search for
        parent_chain: List of parent attribute names in order (from outermost to innermost)
        original_tag: The original XML/HTML tag (optional, for debugging)

    Returns:
        bool: True if the attribute is translatable, False otherwise
    """
    tag_data = DICTIONARY.get(tag_name, {})

    if not isinstance(tag_data, dict):
        logger.debug(f"Tag '{tag_name}' not found in dictionary or not a dictionary")
        return False

    attributes = tag_data.get("attributes", {})

    # Navigate through the parent chain if provided
    if parent_chain:
        chain_str = " -> ".join(parent_chain)
        logger.debug(f"Checking nested attribute '{attr_name}' with parent chain: {chain_str} for tag '{tag_name}' (REG. TRANSLATE)")
        
        current_attrs = attributes
        for parent in parent_chain:
            if parent in current_attrs:
                parent_data = current_attrs[parent]
                if isinstance(parent_data, dict) and "attributes" in parent_data and parent_data["attributes"] != {}:
                    current_attrs = parent_data["attributes"]
            else:
                logger.debug(f"Parent attribute '{parent}' not found in chain")
                return False
        
        attr_data = current_attrs.get(attr_name, {})
    else:
        # Check top-level attributes
        logger.debug(f"Checking top-level attribute '{attr_name}' for tag '{tag_name}' (REG. TRANSLATE)")
        attr_data = attributes.get(attr_name, {})

    result = attr_data.get("translatable", False) and \
             not attr_data.get("encoding", False) and \
             not attr_data.get("delimited_encoding", False)

    logger.debug(f"Attribute '{attr_name}' for tag '{tag_name}' is_translatable: {result}")
    return result


def is_attribute_to_decode_json(tag_name: str, attr_name: str, parent_chain: List[str] = None, original_tag: str = None) -> bool:
    """
    Checks if an attribute is to be decoded (JSON) by looking it up in the dictionary.
    Handles both top-level and nested attributes with full parent chain support.

    Args:
        tag_name: The name of the XML/HTML tag
        attr_name: The name of the attribute to search for
        parent_chain: List of parent attribute names in order (from outermost to innermost)
        original_tag: The original XML/HTML tag (optional, for debugging)

    Returns:
        bool: True if the attribute should be decoded as JSON, False otherwise
    """
    tag_data = DICTIONARY.get(tag_name, {})

    if not isinstance(tag_data, dict):
        logger.debug(f"Tag '{tag_name}' not found in dictionary or not a dictionary")
        return False

    attributes = tag_data.get("attributes", {})

    # Navigate through the parent chain if provided
    if parent_chain:
        chain_str = " -> ".join(parent_chain)
        logger.debug(f"Checking nested attribute '{attr_name}' with parent chain: {chain_str} for tag '{tag_name}' (JSON)")
        
        current_attrs = attributes
        for parent in parent_chain:
            if parent in current_attrs:
                parent_data = current_attrs[parent]
                if isinstance(parent_data, dict) and "attributes" in parent_data and parent_data["attributes"] != {}:
                    current_attrs = parent_data["attributes"]
            else:
                logger.debug(f"Parent attribute '{parent}' not found in chain")
                return False
        
        attr_data = current_attrs.get(attr_name, {})
    else:
        # Check top-level attributes
        logger.debug(f"Checking top-level attribute '{attr_name}' for tag '{tag_name}' (JSON)")
        attr_data = attributes.get(attr_name, {})

    result = attr_data.get("translatable", False) and \
             attr_data.get("encoding", False) and \
             not attr_data.get("delimited_encoding", False)

    logger.debug(f"Attribute '{attr_name}' for tag '{tag_name}' is_json_encoded: {result}")
    return result


def is_attribute_to_decode_pipe(tag_name: str, attr_name: str, parent_chain: List[str] = None, original_tag: str = None) -> bool:
    """
    Checks if an attribute is to be decoded (pipe-delimited) by looking it up in the dictionary.
    Handles both top-level and nested attributes with full parent chain support.

    Args:
        tag_name: The name of the XML/HTML tag
        attr_name: The name of the attribute to search for
        parent_chain: List of parent attribute names in order (from outermost to innermost)
        original_tag: The original XML/HTML tag (optional, for debugging)

    Returns:
        bool: True if the attribute should be decoded as pipe-delimited, False otherwise
    """
    tag_data = DICTIONARY.get(tag_name, {})

    if not isinstance(tag_data, dict):
        logger.debug(f"Tag '{tag_name}' not found in dictionary or not a dictionary")
        return False

    attributes = tag_data.get("attributes", {})

    # Navigate through the parent chain if provided
    if parent_chain:
        chain_str = " -> ".join(parent_chain)
        logger.debug(f"Checking nested attribute '{attr_name}' with parent chain: {chain_str} for tag '{tag_name}' (PIPE)")
        
        current_attrs = attributes
        for parent in parent_chain:
            if parent in current_attrs:
                parent_data = current_attrs[parent]
                if isinstance(parent_data, dict) and "attributes" in parent_data and parent_data["attributes"] != {}:
                    current_attrs = parent_data["attributes"]
            else:
                logger.debug(f"Parent attribute '{parent}' not found in chain")
                return False
        
        attr_data = current_attrs.get(attr_name, {})
    else:
        # Check top-level attributes
        logger.debug(f"Checking top-level attribute '{attr_name}' for tag '{tag_name}' (PIPE)")
        attr_data = attributes.get(attr_name, {})

    result = attr_data.get("translatable", False) and \
             not attr_data.get("encoding", False) and \
             attr_data.get("delimited_encoding", False)

    logger.debug(f"Attribute '{attr_name}' for tag '{tag_name}' is_pipe_delimited: {result}")
    return result


def is_self_closing(tag: str) -> bool:
    """
    Determines if a tag is self-closing.

    Handles the following formats:
    - HTML void elements (e.g., <br>, <img>)
    - Explicitly self-closed tags (e.g., <h2/>, <h2 />, <h2 attr="value"/>)
    - Custom self-closing tags defined in DICTIONARY

    Args:
        tag: The tag string to check

    Returns:
        bool: True if the tag is self-closing, False otherwise

    Examples:
        >>> is_self_closing('<br>')           # True (void element)
        >>> is_self_closing('<h2/>')          # True (explicit self-close)
        >>> is_self_closing('<h2 />')         # True (explicit self-close with space)
        >>> is_self_closing('<h2 id="1"/>')   # True (explicit self-close with attrs)
        >>> is_self_closing('[mytag/]')       # True (custom tag if in DICTIONARY)
        >>> is_self_closing('<div>')          # False
    """
    if not tag or not isinstance(tag, str):
        logger.warning(f"Invalid tag provided to is_self_closing: {tag}")
        return False

    tag = tag.strip()

    # Check for HTML void elements first
    tag_name_pattern = r'^<([^\s/>]+)'
    tag_name_match = re.match(tag_name_pattern, tag)
    if tag_name_match:
        tag_name = tag_name_match.group(1).lower()
        if tag_name in HTML_VOID_ELEMENTS:
            logger.debug(f"Tag '{tag_name}' is a standard HTML void element")
            return True

    # Check for explicitly self-closed HTML tags
    # Matches: <tag/>, <tag />, <tag attr="value"/>, <tag attr="value" />
    html_self_close = re.compile(r'^<[^>]+?/>$')
    if html_self_close.match(tag):
        logger.debug(f"Tag '{tag}' is explicitly self-closed")
        return True
    
    SELF_CLOSING_TAGS = {
    tag for tag, data in DICTIONARY.items() if data.get("self_closing", False)
    }

    # Check for custom self-contained tags
    # Matches: [tag], [tag/], [tag /], [tag attr="value"], [tag attr="value"/]
    custom_pattern = re.compile(r'^\[([^\s\]]+)(?:\s+[^\]]+)?\]$')
    custom_match = custom_pattern.match(tag)
    if custom_match:
        custom_tag = custom_match.group(1).rstrip('/')
        if custom_tag in SELF_CLOSING_TAGS:
            logger.debug(f"Tag '{custom_tag}' is a custom self-closing tag")
            return True

    logger.debug(f"Tag '{tag}' is not self-closing")
    return False


def truncate_after_last_content(text: str) -> str:
    """
    Truncates text after the last occurrence of '</content>'.

    This function is used to remove extra content that might be added by
    AI translation models trying to fix HTML code.

    Args:
        text: The text to truncate

    Returns:
        str: The truncated text, or the original text if '</content>' is not found
    """
    # Find the last occurrence of '</content>'
    last_idx = text.rfind('</content>')

    if last_idx == -1:
        logger.debug("No '</content>' tag found, returning original text")
        return text  # No '</content>' found

    # Calculate the final position (including '</content>')
    end_pos = last_idx + len('</content>')

    # If we're truncating, log it
    if end_pos < len(text):
        logger.debug(f"Truncating text after last '</content>' tag, removing {len(text) - end_pos} characters")

    # Slice the string up to the end of '</content>'
    return text[:end_pos]


def translate_pipe_delimited_attributes(
    attr_value: str,
    source: str,
    targets: List[str],
    translator: TextTranslator,
    tag_name: str,
    parent_chain: List[str] = None,
    mode: Mode = Mode.STANDARD_TURBO,
    original_tag: str = None
) -> Dict[str, str]:
    """
    Translates specific attributes in a URL-encoded and pipe-delimited string.

    Args:
        attr_value: URL-encoded string with pipe-delimited attributes (e.g., 'url:value|title:value|target:value')
        source: Source language
        targets: List of target languages
        translator: TextTranslator instance
        tag_name: The name of the XML/HTML tag
        parent_chain: List of parent attribute names in order (from outermost to innermost)
        mode: Translation mode

    Returns:
        Dictionary with translations for each target language
    """
    # Create dictionary for translations
    translations = {lang: [] for lang in targets}

    # Split string into key-value pairs
    pairs = []
    for pair in attr_value.split('|'):
        try:
            # Try to split by colon, requiring at least one colon
            if ':' in pair:
                pairs.append(pair.split(':', 1))
            else:
                # If no colon, just add the pair as is to preserve the structure
                logger.warning(f"Invalid pipe-delimited attribute format (no colon): {pair}")
                for lang in targets:
                    translations[lang].append(pair)
        except Exception as e:
            logger.warning(f"Error processing pipe-delimited pair: {pair}. Error: {str(e)}")
            continue

    # Process each key-value pair
    for pair in pairs:
        try:
            # Make sure the pair has both key and value
            if len(pair) != 2:
                logger.warning(f"Invalid pipe-delimited pair format: {pair}")
                # Add the original pair to preserve structure
                for lang in targets:
                    translations[lang].append(':'.join(pair) if len(pair) > 1 else pair[0])
                continue

            key, value = pair

            # Decode URL-encoded value before processing
            decoded_value = urllib.parse.unquote(value)

            # Create new parent chain for nested processing
            new_parent_chain = (parent_chain or []) + [key]

            # Check if this is a nested encoding that needs special processing
            if is_attribute_to_decode_json(tag_name, key, new_parent_chain, original_tag):
                # This is a JSON-encoded attribute within a pipe-delimited string
                try:
                    # Parse the JSON
                    json_data = json.loads(decoded_value)

                    # Create a deep copy for each target language
                    translated_jsons = {lang: copy.deepcopy(json_data) for lang in targets}

                    # Process each item in the JSON
                    if isinstance(json_data, list):
                        for item_index, item in enumerate(json_data):
                            if isinstance(item, dict):
                                for json_key, json_value in item.items():
                                    # Check if this JSON attribute should be translated
                                    # Use the current parent chain (new_parent_chain) not extending it further
                                    if (is_attribute_to_translate(tag_name, json_key, new_parent_chain, original_tag)):

                                        # Translate the JSON value
                                        translated_json_values = translator.translate(
                                            {"text": json_value},
                                            source,
                                            targets,
                                            mode=mode,
                                            text_type=TextType.HTML
                                        )["text"]

                                        # Update the translated JSON for each language
                                        for lang in targets:
                                            translated_jsons[lang][item_index][json_key] = translated_json_values[lang]

                    # Re-encode the translated JSON for each language
                    for lang in targets:
                        encoded_json = urllib.parse.quote(json.dumps(translated_jsons[lang]))
                        translations[lang].append(f"{key}:{encoded_json}")

                except (json.JSONDecodeError, Exception) as e:
                    logger.warning(f"Error processing JSON in pipe-delimited attribute: {e}")
                    # Fallback to original value if JSON processing fails
                    for lang in targets:
                        translations[lang].append(f"{key}:{value}")

            elif is_attribute_to_decode_pipe(tag_name, key, new_parent_chain, original_tag):
                # This is a nested pipe-delimited attribute - handle recursively
                try:
                    # Recursively process the nested pipe-delimited string
                    nested_translations = translate_pipe_delimited_attributes(
                        decoded_value,
                        source,
                        targets,
                        translator,
                        tag_name,
                        new_parent_chain,  # Pass the extended chain
                        mode,
                        original_tag
                    )

                    # Add the translated nested pipe-delimited strings
                    for lang in targets:
                        encoded_nested = urllib.parse.quote(nested_translations[lang])
                        translations[lang].append(f"{key}:{encoded_nested}")

                except Exception as e:
                    logger.warning(f"Error processing nested pipe-delimited attribute: {e}")
                    # Fallback to original value if processing fails
                    for lang in targets:
                        translations[lang].append(f"{key}:{value}")
            else:
                # Check if this attribute should be translated based on dictionary configuration
                should_translate = (
                    is_attribute_to_translate(tag_name, key, new_parent_chain, original_tag) or
                    is_attribute_to_decode_pipe(tag_name, key, new_parent_chain, original_tag) or
                    is_attribute_to_decode_json(tag_name, key, new_parent_chain, original_tag)
                )

                if should_translate:
                    # Translate the value
                    translated_values = translator.translate(
                        {"text": decoded_value},
                        source,
                        targets,
                        mode=mode,
                        text_type=TextType.HTML
                    )["text"]

                    # Add translations for each language
                    for lang in targets:
                        # URL encode only the value after the colon
                        encoded_value = urllib.parse.quote(translated_values[lang])
                        translations[lang].append(f"{key}:{encoded_value}")
                else:
                    # For values not to translate, keep the original URL encoding
                    for lang in targets:
                        translations[lang].append(f"{key}:{value}")
                        
        except Exception as e:
            logger.warning(f"Error processing key-value pair: {pair}. Error: {str(e)}")
            # Add the original pair to preserve structure
            for lang in targets:
                translations[lang].append(':'.join(pair) if len(pair) > 1 else pair[0])

    # Reconstruct strings using pipe as separator (without encoding it)
    result = {}
    for lang in targets:
        result[lang] = "|".join(translations[lang])

    return result


def extract_tag_name(tag: str) -> str:
    """
    Extracts the tag name from a full tag string.

    Handles tags enclosed in angle brackets (<>) or square brackets ([]),
    including self-closing and closing tags with attributes in double quotes.

    Args:
        tag: The tag string to extract the name from

    Returns:
        str: The extracted tag name, or an empty string if no match is found

    Examples:
        '<vc_column_text>' -> 'vc_column_text'
        '</vc_column_text>' -> 'vc_column_text'
        '[elx_hint]' -> 'elx_hint'
        '[/elx_hint]' -> 'elx_hint'
        '<vc_column_text title="example">' -> 'vc_column_text'
        '[elx_hint title="example"]' -> 'elx_hint'
    """
    if not tag or not isinstance(tag, str):
        logger.warning(f"Invalid tag provided to extract_tag_name: {tag}")
        return ""

    tag = tag.strip()

    try:
        # Match tags enclosed in angle brackets (e.g., <tag>, </tag>, <tag attr="value>)
        angle_bracket_match = re.match(r'<\s*/?\s*([a-zA-Z0-9_:-]+)', tag)
        if angle_bracket_match:
            tag_name = angle_bracket_match.group(1)
            logger.debug(f"Extracted tag name '{tag_name}' from angle bracket tag")
            return tag_name

        # Match tags enclosed in square brackets (e.g., [tag], [/tag], [tag attr="value"])
        square_bracket_match = re.match(r'\[\s*/?\s*([a-zA-Z0-9_:-]+)', tag)
        if square_bracket_match:
            tag_name = square_bracket_match.group(1)
            logger.debug(f"Extracted tag name '{tag_name}' from square bracket tag")
            return tag_name

        # If no match, log a warning and return an empty string
        logger.warning(f"Could not extract tag name from: {tag}")
        return ""
    except Exception as e:
        logger.error(f"Error extracting tag name from '{tag}': {e}")
        return ""


def fallback_tag_replacement(placeholder: str, original_tag: str, text: str) -> str:
    """
    Estrae l'ID dalla div_string e sostituisce la prima occorrenza
    di un tag div con quell'ID specifico nel testo.

    Args:
        testo (str): Il testo in cui cercare
        div_string (str): Stringa contenente il div da cui estrarre l'ID (es: '<div id="470">')
        nuovo_testo (str): Il testo con cui sostituire

    Returns:
        str: Il testo con la sostituzione effettuata, None se ID non trovato
    """
    # Estrae l'ID dalla stringa div
    id_target = extract_id_from_div(placeholder)

    if id_target is None:
        print(f"Errore: impossibile estrarre l'ID da '{extract_id_from_div}'")
        return text

    # Pattern regex che gestisce tutti i casi:
    # - spazi opzionali dopo <div o </div
    # - spazi opzionali attorno all'attributo id
    # - virgolette opzionali attorno al valore dell'id
    # - spazi opzionali prima della chiusura >
    # - > opzionale alla fine
    pattern = rf'</?\s*div\s+id\s*=\s*["\']?{re.escape(id_target)}[^"\'>\s]*["\']?\s*>'

    # Sostituisce solo la prima occorrenza
    risultato = re.sub(pattern, original_tag, text, count=1)

    return risultato


def extract_id_from_div(div_string:str) -> str | None:
    """
    Estrae l'ID da una stringa che rappresenta un tag div.

    Args:
        div_string (str): Stringa contenente il tag div (es: '<div id="470">' o '</div id="471">')

    Returns:
        str: L'ID estratto, None se non trovato
    """
    # Pattern per estrarre l'ID da tag div di apertura o chiusura
    # Gestisce spazi, virgolette opzionali, > opzionale
    pattern = r'</?\s*div\s+id\s*=\s*["\']?([^"\'>\s]+)["\']?\s*>?'

    match = re.search(pattern, div_string, re.IGNORECASE)

    if match:
        return match.group(1)
    else:
        return None


class XMLTranslator(Translator, metaclass=Singleton):
    def __init__(self, verify=True) -> None:
        self.translator = TextTranslator(verify=verify)
        self.translate_calls = 0  # Counter for translation calls

    def _increment_and_log_translate_calls(self):
        self.translate_calls += 1
        logger.debug(f"TextTranslator.translate called {self.translate_calls} times")

    @property
    def supported_languages(self) -> List[Language]:
        return self.translator.supported_languages

    def translate(
        self,
        texts: Dict[str, str],
        source: str,
        targets: List[str],
        mode: Mode = Mode.STANDARD_TURBO,
    ) -> Dict[str, str]:
        """
        Translates a dictionary of texts while preserving HTML/XML tags and handling JSON-encoded attributes.
        """
        self.translate_calls = 0

        logger.info(f"Starting translation: source='{source}', targets={targets}, mode={mode}")
        logger.info(f"Number of texts to translate: {len(texts)}")

        placeholders = {}
        text_with_placeholders = {}

        for text_id, text in texts.items():
            logger.info(f"Processing text ID: {text_id}")
            logger.debug(f"Original text length: {len(text)} characters")

            # Step 0: Remove CDATA if present
            cdata_opening_pattern = r'<content><!\[CDATA\['
            cdata_closing_pattern = r'\]\]></content>'
            is_cdata_present = True if re.search(cdata_opening_pattern, text) else False
            if is_cdata_present:
                logger.debug(f"CDATA detected in text ID: {text_id}, removing CDATA tags")
                text = re.sub(cdata_opening_pattern, '<content>', text)
                text = re.sub(cdata_closing_pattern, '</content>', text)

            # Step 1: Replace tags and pseudo-tags with placeholders
            tag_pattern = r'(\[.*?\]|\<.*?\>)'
            tags = re.findall(tag_pattern, text)
            logger.info(f"Found {len(tags)} tags in text ID: {text_id}")

            placeholders[text_id] = {}
            text_with_placeholders[text_id] = text

            for i, tag in enumerate(tags):
                tag_name = extract_tag_name(tag)
                logger.debug(f"Processing tag {i}: '{tag_name}'")

                if is_self_closing(tag):
                    placeholder = f'<img id="{i}"/>' # placeholder for self-closing tags
                    logger.debug(f"Tag '{tag_name}' is self-closing, using img placeholder")
                elif tag.startswith("</") or tag.startswith("[/"):
                    placeholder = f'</div id="{i}">'
                    logger.debug(f"Tag '{tag_name}' is a closing tag, using closing div placeholder")
                else:
                    placeholder = f'<div id="{i}">'
                    logger.debug(f"Tag '{tag_name}' is an opening tag, using opening div placeholder")

                placeholders[text_id][placeholder] = tag
                text_with_placeholders[text_id] = text_with_placeholders[text_id].replace(tag, placeholder, 1)

            # Step 2: Process placeholders for attributes and JSON
            logger.info(f"Processing attributes for {len(placeholders[text_id])} tags in text ID: {text_id}")

            for placeholder, original_tag in placeholders[text_id].items():
                tag_name = extract_tag_name(original_tag)
                logger.info(f"Processing attributes for tag: '{tag_name}'")

                # Match attributes in the tag
                attribute_pattern = r'(\w+)="(.*?)"'
                attributes = re.findall(attribute_pattern, original_tag)
                logger.info(f"Found {len(attributes)} attributes in tag '{tag_name}'")

                # Dictionary to store translated versions of the tag for each language
                translated_tags = {lang: original_tag for lang in targets}

                # Process each attribute, potentially passing parent chain for nested attributes
                for attr_name, attr_value in attributes:
                    logger.debug(f"Processing attribute '{attr_name}' in tag '{tag_name}'")
                    # Each attribute starts with no parent chain (None) since we're at the top level
                    self.process_attribute(original_tag, attr_name, attr_value,
                                          translated_tags, source, targets, mode, None)

                # Update placeholders with translated tags for each language
                placeholders[text_id][placeholder] = translated_tags
                logger.debug(f"Updated placeholder for tag '{tag_name}' with translated versions")

        # Step 3: Translate the main text
        logger.info("Translating main text with placeholders")
        logger.debug(text_with_placeholders)
        try:
            translated_text = self.translator.translate(
                text_with_placeholders, source, targets, mode=mode, text_type=TextType.HTML
            )
            self._increment_and_log_translate_calls()
            logger.info(f"Main text translation successful")
            logger.debug(translated_text)
        except Exception as e:
            logger.error(f"Error translating main text: {e}")
            # Create a fallback translation dictionary
            translated_text = {}
            for text_id, text in text_with_placeholders.items():
                translated_text[text_id] = {lang: text for lang in targets}

        if logger.isEnabledFor(logging.DEBUG):
            for text_id, text in text_with_placeholders.items():
                logger.debug(f"Original text with placeholders (ID: {text_id}): {text[:100]}..." if len(text) > 100 else f"Original text with placeholders (ID: {text_id}): {text}")

            for text_id, translations in translated_text.items():
                for lang, text in translations.items():
                    logger.debug(f"Translated text (ID: {text_id}, Lang: {lang}): {text[:100]}..." if len(text) > 100 else f"Translated text (ID: {text_id}, Lang: {lang}): {text}")

        logger.info(f"Translation completed - Total calls to TextTranslator.translate: {self.translate_calls}")

        # Step 4: Reinsert original tags with translated attributes
        logger.info("Reinserting original tags with translated attributes")

        for text_id, translations in translated_text.items():
            logger.debug(f"Processing text ID: {text_id} for tag reinsertion")
            translations_with_original_tags = {}

            for lang, text in translations.items():
                logger.debug(f"Processing language: {lang} for text ID: {text_id}")
                placeholder_count = 0

                for placeholder, tag_translations in placeholders[text_id].items():
                    # Use the translated version of the tag for the current language
                    try:
                        original_text = text
                        text = text.replace(placeholder, tag_translations[lang], 1)
                        if original_text != text:
                            placeholder_count += 1
                            logger.debug(f"Replaced placeholder with translated tag for language: {lang}")
                        else:
                            logger.warning(f"Placeholder [{placeholder}] not found in text for language: {lang} try fallback replacement")
                            text = fallback_tag_replacement(placeholder, tag_translations[lang], text)
                    except Exception as e:
                        logger.error(f"Error replacing placeholder in language {lang}: {e}")

                logger.debug(f"Replaced {placeholder_count} placeholders for language: {lang}")

                # Remove extra div eventually added by AI translation model that is trying to fix the html code
                original_length = len(text)
                text = truncate_after_last_content(text)
                if len(text) != original_length:
                    logger.debug(f"Truncated text after last </content> tag, removed {original_length - len(text)} characters")

                # Restore CDATA tags if they were removed
                if is_cdata_present:
                    text = re.sub(r'<content>', r'<content><![CDATA[', text)
                    text = re.sub(r'</content>', r']]></content>', text)
                    logger.debug(f"Restored CDATA tags in text ID: {text_id}")

                translations_with_original_tags[lang] = text

            translated_text[text_id] = translations_with_original_tags
            logger.info(f"Completed tag reinsertion for text ID: {text_id}")

        logger.info("Translation process completed successfully")
        return translated_text

    def process_attribute(self, original_tag: str, attr_name: str, attr_value: str,
                         translated_tags: Dict[str, str], source: str, targets: List[str],
                         mode: Mode = Mode.STANDARD_TURBO, parent_chain: List[str] = None) -> None:
        """
        Process a single attribute for translation, handling different attribute types
        (regular, pipe-delimited, and JSON-encoded).

        Args:
            original_tag: The original XML/HTML tag
            attr_name: The name of the attribute to process
            attr_value: The value of the attribute
            translated_tags: Dictionary to store translated tags for each language
            source: Source language
            targets: List of target languages
            mode: Translation mode
            parent_chain: List of parent attribute names in order (from outermost to innermost)
        """
        tag_name = extract_tag_name(original_tag)
        chain_str = " -> ".join(parent_chain) if parent_chain else "None"
        logger.info(f"Processing attribute '{attr_name}' for tag '{tag_name}' with parent chain: {chain_str}")

        # Check which type of attribute we are dealing with
        is_regular = is_attribute_to_translate(tag_name, attr_name, None, original_tag) or is_attribute_to_translate(tag_name, attr_name, parent_chain, original_tag)
        is_pipe = is_attribute_to_decode_pipe(tag_name, attr_name, None, original_tag) or is_attribute_to_decode_pipe(tag_name, attr_name, parent_chain, original_tag)
        is_json = is_attribute_to_decode_json(tag_name, attr_name, None, original_tag) or is_attribute_to_decode_json(tag_name, attr_name, parent_chain, original_tag)

        logger.debug(f"Attribute '{attr_name}' types: regular={is_regular}, pipe={is_pipe}, json={is_json}")

        if is_regular:
            # Regular attribute translation
            logger.info(f"Translating regular attribute '{attr_name}' with value: {attr_value[:50]}..." if len(attr_value) > 50 else f"Translating regular attribute '{attr_name}' with value: {attr_value}")

            try:
                translated_values = self.translator.translate(
                    {"text": attr_value}, source, targets, mode=mode, text_type=TextType.HTML
                )["text"]
                self._increment_and_log_translate_calls()

                for lang, translated_value in translated_values.items():
                    logger.debug(f"Translated '{attr_name}' to '{lang}': {translated_value[:50]}..." if len(translated_value) > 50 else f"Translated '{attr_name}' to '{lang}': {translated_value}")

                    # Use re.escape to handle special characters in attribute names
                    pattern = rf'({re.escape(attr_name)}=")(.*?)(")'
                    replacement = rf'\g<1>{translated_value}\g<3>'

                    try:
                        translated_tags[lang] = re.sub(
                            pattern,
                            replacement,
                            translated_tags[lang]
                        )
                    except Exception as e:
                        logger.error(f"Error replacing attribute '{attr_name}' in tag: {e}")

                logger.info(f"Successfully translated regular attribute '{attr_name}'")
            except Exception as e:
                logger.error(f"Error translating regular attribute '{attr_name}': {e}")

        elif is_pipe:
            # Handle pipe-delimited attributes
            logger.info(f"Translating pipe-delimited attribute '{attr_name}' with value: {attr_value[:50]}..." if len(attr_value) > 50 else f"Translating pipe-delimited attribute '{attr_name}' with value: {attr_value}")

            try:
                # Create new parent chain for nested processing
                new_parent_chain = (parent_chain or []) + [attr_name]
                
                translated_values = translate_pipe_delimited_attributes(
                    attr_value, source, targets, self.translator, tag_name, new_parent_chain, mode=mode, original_tag=original_tag
                )

                for lang, translated_value in translated_values.items():
                    logger.debug(f"Translated pipe-delimited '{attr_name}' to '{lang}': {translated_value[:50]}..." if len(translated_value) > 50 else f"Translated pipe-delimited '{attr_name}' to '{lang}': {translated_value}")

                    # Use re.escape to handle special characters in attribute names
                    pattern = rf'({re.escape(attr_name)}=")(.*?)(")'
                    replacement = rf'\g<1>{translated_value}\g<3>'

                    try:
                        translated_tags[lang] = re.sub(
                            pattern,
                            replacement,
                            translated_tags[lang]
                        )
                    except Exception as e:
                        logger.error(f"Error replacing pipe-delimited attribute '{attr_name}' in tag: {e}")

                logger.info(f"Successfully translated pipe-delimited attribute '{attr_name}'")
            except Exception as e:
                logger.error(f"Error translating pipe-delimited attribute '{attr_name}': {e}")

        elif is_json:
            # Handle JSON-encoded attributes
            logger.info(f"Processing JSON-encoded attribute '{attr_name}' with value: {attr_value[:50]}..." if len(attr_value) > 50 else f"Processing JSON-encoded attribute '{attr_name}' with value: {attr_value}")

            try:
                # Create new parent chain for nested processing
                new_parent_chain = (parent_chain or []) + [attr_name]
                
                self._process_json_attribute(
                    tag_name, attr_name, attr_value,
                    translated_tags, source, targets, mode, new_parent_chain, original_tag
                )
                logger.info(f"Successfully processed JSON-encoded attribute '{attr_name}'")
            except json.JSONDecodeError as e:
                logger.error(f"JSON decode error for attribute '{attr_name}': {e}")
            except Exception as e:
                logger.error(f"Error processing JSON-encoded attribute '{attr_name}': {e}")
        else:
            logger.info(f"Attribute '{attr_name}' is not marked for translation in the dictionary")

    def _process_json_attribute(self, tag_name: str, attr_name: str, attr_value: str,
                                translated_tags: Dict[str, str], source: str, targets: List[str],
                                mode: Mode, parent_chain: List[str] = None, original_tag: str = None) -> None:
        """
        Process a JSON-encoded attribute, handling nested translations.

        Args:
            tag_name: The name of the XML/HTML tag
            attr_name: The name of the attribute to process
            attr_value: The value of the attribute
            translated_tags: Dictionary to store translated tags for each language
            source: Source language
            targets: List of target languages
            mode: Translation mode
            parent_chain: List of parent attribute names in order (from outermost to innermost)
            original_tag: The original XML/HTML tag (optional, for debugging)
        """
        chain_str = " -> ".join(parent_chain) if parent_chain else "None"
        logger.info(f"Processing JSON attribute '{attr_name}' for tag '{tag_name}' with parent chain: {chain_str}")

        try:
            decoded_value = urllib.parse.unquote(attr_value)
            logger.debug(f"URL-decoded value: {decoded_value[:100]}..." if len(decoded_value) > 100 else f"URL-decoded value: {decoded_value}")

            decoded_json = json.loads(decoded_value)
            logger.debug(f"JSON structure type: {type(decoded_json).__name__}, length: {len(decoded_json) if isinstance(decoded_json, (list, dict)) else 'N/A'}")

            translated_jsons = {lang: copy.deepcopy(decoded_json) for lang in targets}

            if not isinstance(decoded_json, list):
                logger.warning(f"JSON structure is not a list, it's a {type(decoded_json).__name__}. This may cause issues.")
                return

            for i, item in enumerate(decoded_json):
                if not isinstance(item, dict):
                    logger.warning(f"Item at index {i} is not a dictionary, it's a {type(item).__name__}. Skipping.")
                    continue

                logger.debug(f"Processing item {i} with keys: {list(item.keys())}")

                for json_attr_name, json_attr_value in item.items():
                    # Check if the attribute should be translated, considering the full parent chain
                    should_translate = (
                        is_attribute_to_translate(tag_name, json_attr_name, parent_chain, original_tag) or
                        is_attribute_to_decode_json(tag_name, json_attr_name, parent_chain, original_tag) or
                        is_attribute_to_decode_pipe(tag_name, json_attr_name, parent_chain, original_tag)
                    )

                    logger.debug(f"Attribute '{json_attr_name}' should_translate: {should_translate}")

                    if should_translate:
                        logger.info(f"Translating JSON attribute '{json_attr_name}' with value: {str(json_attr_value)[:50]}..." if len(str(json_attr_value)) > 50 else f"Translating JSON attribute '{json_attr_name}' with value: {json_attr_value}")

                        try:
                            # Create new parent chain for nested processing
                            new_parent_chain = (parent_chain or []) + [json_attr_name]
                            
                            translated_values = self._translate_json_value(
                                json_attr_value, source, targets, mode, tag_name, new_parent_chain, original_tag
                            )
                            self._increment_and_log_translate_calls()

                            for lang, translated_value in translated_values.items():
                                logger.debug(f"Translated '{json_attr_name}' to '{lang}': {str(translated_value)[:50]}..." if len(str(translated_value)) > 50 else f"Translated '{json_attr_name}' to '{lang}': {translated_value}")
                                translated_jsons[lang][i][json_attr_name] = translated_value
                        except Exception as e:
                            logger.error(f"Error translating JSON attribute '{json_attr_name}': {str(e)}")

            # Re-encode JSON for each language and update tags
            for lang in targets:
                new_encoded_json = urllib.parse.quote(json.dumps(translated_jsons[lang]))
                logger.debug(f"Replacing JSON in '{lang}' tag")
                translated_tags[lang] = translated_tags[lang].replace(attr_value, new_encoded_json, 1)

            logger.info(f"Successfully processed JSON attribute '{attr_name}'")

        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error for attribute '{attr_name}': {str(e)}")
        except Exception as e:
            logger.error(f"Error processing JSON attribute '{attr_name}': {str(e)}")

    def _translate_json_value(self, value: str, source: str, targets: List[str],
                               mode: Mode, tag_name: str = None, parent_chain: List[str] = None, original_tag: str = None) -> Dict[str, str]:
        """
        Translate a JSON value, handling both pipe-delimited and regular strings.

        Args:
            value: The value to translate
            source: Source language
            targets: List of target languages
            mode: Translation mode
            tag_name: The name of the XML/HTML tag (optional)
            parent_chain: List of parent attribute names in order (from outermost to innermost)
            original_tag: The original XML/HTML tag (optional, for debugging)

        Returns:
            Dictionary with translations for each target language
        """
        logger.debug(f"Translating JSON value: {str(value)[:50]}..." if isinstance(value, str) and len(value) > 50 else f"Translating JSON value: {value}")

        # Check if this is a JSON string that needs to be decoded
        if isinstance(value, str):
            try:
                # Try to parse as JSON first
                if value.strip().startswith('{') or value.strip().startswith('['):
                    logger.debug("Value appears to be JSON, attempting to parse")
                    try:
                        json_data = json.loads(value)
                        # If it's valid JSON, process it recursively
                        if isinstance(json_data, (dict, list)):
                            logger.info(f"Processing nested JSON structure of type {type(json_data).__name__}")
                            # Create a temporary XMLTranslator to process this nested JSON
                            temp_translator = XMLTranslator()
                            # Create a temporary tag dictionary for the result
                            temp_tags = {lang: value for lang in targets}
                            # Process the JSON attribute
                            temp_translator._process_json_attribute(
                                tag_name,
                                "nested",  # Use a consistent attribute name for nested processing
                                value,
                                temp_tags,
                                source,
                                targets,
                                mode,
                                parent_chain,  # Pass the parent chain consistently
                                original_tag
                            )
                            # Extract the processed values
                            result = {}
                            for lang, translated_value in temp_tags.items():
                                # The value is the part after the attribute name and equals sign
                                result[lang] = translated_value
                                logger.debug(f"Nested JSON translation for '{lang}': {str(translated_value)[:50]}..." if len(str(translated_value)) > 50 else f"Nested JSON translation for '{lang}': {translated_value}")
                            return result
                    except json.JSONDecodeError as e:
                        # Not valid JSON, continue with other checks
                        logger.debug(f"Not valid JSON despite appearance: {e}")
                        pass

                # Check if it's a pipe-delimited string
                if re.match(r'.+:.+\|', value):
                    logger.info("Value appears to be pipe-delimited, processing with pipe-delimited handler")
                    result = translate_pipe_delimited_attributes(
                        value, source, targets, self.translator, tag_name, parent_chain, mode=mode, original_tag=original_tag
                    )
                    logger.debug(f"Pipe-delimited translation result: {result}")
                    return result
            except Exception as e:
                # Log the error and fall back to regular translation
                logger.warning(f"Error processing nested value, falling back to regular translation: {e}")
        else:
            logger.debug(f"Value is not a string, it's a {type(value).__name__}")

        # Default case: regular translation
        logger.info("Using regular translation for value")
        try:
            result = self.translator.translate(
                {"text": str(value)}, source, targets, mode=mode, text_type=TextType.HTML
            )["text"]
            logger.debug(f"Regular translation result: {result}")
            return result
        except Exception as e:
            logger.error(f"Error during regular translation: {e}")
            # Return the original value for all languages as fallback
            return {lang: str(value) for lang in targets}


if __name__ == "__main__":
    try:
        # Initialize the translator
        translator = XMLTranslator(verify=False)

        # Set up file paths
        folder = os.path.join(os.path.dirname(__file__), "..", "test")
        xml_file = os.path.join(folder, "test1.xml")
        # xml_file = os.path.join(folder, "test2.xml")

        # Check if the input file exists
        if not os.path.exists(xml_file):
            logger.error(f"Input file not found: {xml_file}")
            print(f"Error: Input file not found: {xml_file}")
            exit(1)

        # Use lxml.etree.parse to parse the XML file
        try:
            tree = etree.parse(xml_file)
            root = tree.getroot()
            logger.info(f"Successfully parsed XML file: {xml_file}")
        except Exception as e:
            logger.error(f"Error parsing XML file: {e}")
            print(f"Error parsing XML file: {e}")
            exit(1)

        # Convert the XML to a string for translation
        try:
            xml_str = etree.tostring(root, encoding='utf-8', pretty_print=True).decode('utf-8')
            logger.info(f"Converted XML to string, length: {len(xml_str)} characters")

            # Create a dictionary with the XML content
            texts_to_translate = {"xml_content": xml_str}

            # Translate the XML content
            translated_texts = translator.translate(
                texts_to_translate, "en", ["de"], mode=Mode.STANDARD_PROOFREAD
            )

            # Get the translated XML string
            translated_xml_str = translated_texts["xml_content"]["de"]
            logger.info("Translation completed successfully")

            # Parse the translated XML string back to an element
            translated_root = etree.fromstring(translated_xml_str.encode('utf-8'))

        except Exception as e:
            logger.error(f"Error during translation: {e}")
            print(f"Error during translation: {e}")
            exit(1)

        # Save the translated XML to a file
        out_file = os.path.join(folder, "translated_en-de.xml")
        try:
            translated_tree = etree.ElementTree(translated_root)
            translated_tree.write(
                out_file,
                encoding='utf-8',
                xml_declaration=True,
                pretty_print=True
            )
            logger.info(f"Successfully saved translated XML to: {out_file}")
        except Exception as e:
            logger.error(f"Error saving translated XML: {e}")
            print(f"Error saving translated XML: {e}")
            exit(1)

        print(f"Translation completed successfully! Output saved to: {out_file}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"Unexpected error: {e}")
        exit(1)
