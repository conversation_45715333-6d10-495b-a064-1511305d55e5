import re
import argparse
import sys
import json
from collections import defaultdict
from typing import Dict, List, Tuple, Set

class TagAnalyzer:
    def __init__(self, shortcode_descriptors: Dict = None):
        # Pattern per tag XML standard
        self.xml_pattern = r'<([^>]+)>'
        # Pattern per shortcode (tag tra parentesi quadre)
        self.shortcode_pattern = r'\[([^\]]+)\]'
        # Descrittori degli shortcode
        self.shortcode_descriptors = shortcode_descriptors or {}

    def parse_xml_tag(self, tag_content: str) -> Tuple[str, bool, bool]:
        """
        Analizza il contenuto di un tag XML
        Returns: (tag_name, is_closing, is_self_closing)
        """
        tag_content = tag_content.strip()

        # Tag di chiusura
        if tag_content.startswith('/'):
            tag_name = tag_content[1:].split()[0]
            return tag_name, True, False

        # Tag auto-chiudente
        if tag_content.endswith('/'):
            tag_name = tag_content[:-1].split()[0]
            return tag_name, False, True

        # Tag di apertura
        tag_name = tag_content.split()[0]
        return tag_name, False, False

    def parse_shortcode_tag(self, tag_content: str) -> Tuple[str, bool, bool]:
        """
        Analizza il contenuto di un shortcode
        Returns: (tag_name, is_closing, is_self_closing)
        """
        tag_content = tag_content.strip()

        # Tag di chiusura
        if tag_content.startswith('/'):
            tag_name = tag_content[1:].split()[0]
            return tag_name, True, False

        # Tag di apertura - non assumiamo che sia auto-chiudente
        # Lasceremo che l'analisi determini se ha un tag di chiusura corrispondente
        tag_name = tag_content.split()[0]
        return tag_name, False, False

    def analyze_file(self, content: str) -> Dict:
        """
        Analizza tutto il contenuto del file
        """
        results = {
            'xml_tags': defaultdict(list),
            'shortcode_tags': defaultdict(list),
            'unclosed_xml': [],
            'unclosed_shortcode': [],
            'unknown_shortcodes': set(),  # Shortcode non presenti nel descrittore
            'self_closing_mismatches': [], # Incongruenze self-closing
            'summary': {}
        }

        # Stack per tracciare i tag aperti
        xml_stack = []
        shortcode_stack = []

        # Prima passata: raccogli tutti i tag e determina quelli auto-chiudenti
        all_shortcode_tags = defaultdict(lambda: {'opening': 0, 'closing': 0})

        # Conta aperture e chiusure per shortcode
        shortcode_matches = list(re.finditer(self.shortcode_pattern, content))
        for match in shortcode_matches:
            tag_content = match.group(1)
            tag_name, is_closing, _ = self.parse_shortcode_tag(tag_content)

            if is_closing:
                all_shortcode_tags[tag_name]['closing'] += 1
            else:
                all_shortcode_tags[tag_name]['opening'] += 1

        # Determina quali shortcode sono effettivamente auto-chiudenti
        self_closing_shortcodes = set()
        for tag_name, counts in all_shortcode_tags.items():
            if counts['opening'] > 0 and counts['closing'] == 0:
                self_closing_shortcodes.add(tag_name)

        # Trova tutti i tag XML
        xml_matches = re.finditer(self.xml_pattern, content)
        for match in xml_matches:
            tag_content = match.group(1)
            tag_name, is_closing, is_self_closing = self.parse_xml_tag(tag_content)

            position = match.start()
            tag_info = {
                'tag': match.group(0),
                'name': tag_name,
                'position': position,
                'is_closing': is_closing,
                'is_self_closing': is_self_closing
            }

            results['xml_tags'][tag_name].append(tag_info)

            if is_self_closing:
                continue
            elif is_closing:
                # Cerca il tag di apertura corrispondente
                found_opening = False
                for i in range(len(xml_stack) - 1, -1, -1):
                    if xml_stack[i]['name'] == tag_name:
                        xml_stack.pop(i)
                        found_opening = True
                        break
                if not found_opening:
                    results['unclosed_xml'].append(f"Tag di chiusura senza apertura: {match.group(0)} alla posizione {position}")
            else:
                # Tag di apertura
                xml_stack.append(tag_info)

        # Analizza gli shortcode con la nuova logica
        for match in shortcode_matches:
            tag_content = match.group(1)
            tag_name, is_closing, _ = self.parse_shortcode_tag(tag_content)

            # Determina se è auto-chiudente basandosi sull'analisi precedente
            is_self_closing = tag_name in self_closing_shortcodes

            position = match.start()
            tag_info = {
                'tag': match.group(0),
                'name': tag_name,
                'position': position,
                'is_closing': is_closing,
                'is_self_closing': is_self_closing and not is_closing
            }

            results['shortcode_tags'][tag_name].append(tag_info)

            if is_self_closing and not is_closing:
                continue
            elif is_closing:
                # Cerca il tag di apertura corrispondente
                found_opening = False
                for i in range(len(shortcode_stack) - 1, -1, -1):
                    if shortcode_stack[i]['name'] == tag_name:
                        shortcode_stack.pop(i)
                        found_opening = True
                        break
                if not found_opening:
                    results['unclosed_shortcode'].append(f"Shortcode di chiusura senza apertura: {match.group(0)} alla posizione {position}")
            else:
                # Tag di apertura (solo se non è auto-chiudente)
                if not is_self_closing:
                    shortcode_stack.append(tag_info)

        # I tag rimasti negli stack sono quelli non chiusi
        for tag_info in xml_stack:
            results['unclosed_xml'].append(f"Tag XML non chiuso: {tag_info['tag']} alla posizione {tag_info['position']}")

        for tag_info in shortcode_stack:
            results['unclosed_shortcode'].append(f"Shortcode non chiuso: {tag_info['tag']} alla posizione {tag_info['position']}")

        # Genera riassunto
        results['summary'] = {
            'total_xml_tags': sum(len(tags) for tags in results['xml_tags'].values()),
            'total_shortcode_tags': sum(len(tags) for tags in results['shortcode_tags'].values()),
            'unique_xml_tags': len(results['xml_tags']),
            'unique_shortcode_tags': len(results['shortcode_tags']),
            'unclosed_xml_count': len([x for x in results['unclosed_xml'] if 'non chiuso' in x]),
            'unclosed_shortcode_count': len([x for x in results['unclosed_shortcode'] if 'non chiuso' in x])
        }

        # Durante l'analisi degli shortcode, verifica presenza nel descrittore
        for tag_name, tags in results['shortcode_tags'].items():
            if tag_name not in self.shortcode_descriptors:
                results['unknown_shortcodes'].add(tag_name)
            else:
                # Verifica coerenza self-closing
                descriptor = self.shortcode_descriptors[tag_name]
                actual_self_closing = all(t['is_self_closing'] for t in tags)
                if descriptor['self_closing'] != actual_self_closing:
                    results['self_closing_mismatches'].append({
                        'tag': tag_name,
                        'expected': descriptor['self_closing'],
                        'found': actual_self_closing
                    })

        # Aggiorna il summary con le nuove informazioni
        results['summary'].update({
            'unknown_shortcodes_count': len(results['unknown_shortcodes']),
            'self_closing_mismatches_count': len(results['self_closing_mismatches'])
        })

        return results

    def print_results(self, results: Dict):
        """
        Stampa i risultati in modo leggibile
        """
        print("=" * 60)
        print("ANALISI TAG XML E SHORTCODE")
        print("=" * 60)

        # Riassunto
        summary = results['summary']
        print(f"\n📊 RIASSUNTO:")
        print(f"   • Tag XML totali: {summary['total_xml_tags']} ({summary['unique_xml_tags']} unici)")
        print(f"   • Shortcode totali: {summary['total_shortcode_tags']} ({summary['unique_shortcode_tags']} unici)")
        print(f"   • Tag XML non chiusi: {summary['unclosed_xml_count']}")
        print(f"   • Shortcode non chiusi: {summary['unclosed_shortcode_count']}")

        # Tag XML
        if results['xml_tags']:
            print(f"\n🏷️  TAG XML TROVATI:")
            for tag_name, tags in sorted(results['xml_tags'].items()):
                count = len(tags)
                opening_count = len([t for t in tags if not t['is_closing'] and not t['is_self_closing']])
                closing_count = len([t for t in tags if t['is_closing']])
                self_closing_count = len([t for t in tags if t['is_self_closing']])

                print(f"   • {tag_name}: {count} occorrenze")
                if opening_count > 0:
                    print(f"     - Apertura: {opening_count}")
                if closing_count > 0:
                    print(f"     - Chiusura: {closing_count}")
                if self_closing_count > 0:
                    print(f"     - Auto-chiudenti: {self_closing_count}")

        # Shortcode
        if results['shortcode_tags']:
            print(f"\n📦 SHORTCODE TROVATI:")
            for tag_name, tags in sorted(results['shortcode_tags'].items()):
                count = len(tags)
                opening_count = len([t for t in tags if not t['is_closing'] and not t['is_self_closing']])
                closing_count = len([t for t in tags if t['is_closing']])
                self_closing_count = len([t for t in tags if t['is_self_closing']])

                print(f"   • {tag_name}: {count} occorrenze")
                if opening_count > 0:
                    print(f"     - Apertura: {opening_count}")
                if closing_count > 0:
                    print(f"     - Chiusura: {closing_count}")
                if self_closing_count > 0:
                    print(f"     - Auto-chiudenti: {self_closing_count}")

        # Tag non chiusi
        if results['unclosed_xml'] or results['unclosed_shortcode']:
            print(f"\n⚠️  PROBLEMI RILEVATI:")

            if results['unclosed_xml']:
                print("   TAG XML:")
                for issue in results['unclosed_xml']:
                    print(f"     • {issue}")

            if results['unclosed_shortcode']:
                print("   SHORTCODE:")
                for issue in results['unclosed_shortcode']:
                    print(f"     • {issue}")
        else:
            print(f"\n✅ Nessun problema rilevato! Tutti i tag sono correttamente chiusi.")

        # Aggiungi sezione per shortcode sconosciuti
        if results['unknown_shortcodes']:
            print("\n⚠️ SHORTCODE NON DESCRITTI:")
            for tag in sorted(results['unknown_shortcodes']):
                print(f"   • {tag}")

        # Aggiungi sezione per incongruenze self-closing
        if results['self_closing_mismatches']:
            print("\n⚠️ INCONGRUENZE SELF-CLOSING:")
            for mismatch in results['self_closing_mismatches']:
                print(f"   • {mismatch['tag']}:")
                print(f"     - Atteso: {'self-closing' if mismatch['expected'] else 'non self-closing'}")
                print(f"     - Trovato: {'self-closing' if mismatch['found'] else 'non self-closing'}")

def parse_arguments():
    """
    Gestisce i parametri da riga di comando
    """
    parser = argparse.ArgumentParser(
        description='Analizza tag XML e shortcode in un file',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Esempi di utilizzo:
  python tag_analyzer.py file.xml
  python tag_analyzer.py file.xml -o risultati.txt
  python tag_analyzer.py file.xml --descriptors shortcodes.json
        """
    )

    parser.add_argument(
        'input_file',
        help='File da analizzare (XML o contenente shortcode)'
    )

    parser.add_argument(
        '-o', '--output',
        help='File di output per salvare i risultati (opzionale)'
    )

    parser.add_argument(
        '--auto-save',
        action='store_true',
        help='Salva automaticamente i risultati senza chiedere conferma'
    )

    parser.add_argument(
        '--encoding',
        default='utf-8',
        help='Codifica del file di input (default: utf-8)'
    )

    # Aggiungi argomento per il file dei descrittori
    parser.add_argument(
        '--descriptors',
        help='File JSON contenente i descrittori degli shortcode'
    )

    return parser.parse_args()

def main():
    # Gestisci i parametri da riga di comando
    args = parse_arguments()

    try:
        # Carica i descrittori se specificati
        shortcode_descriptors = {}
        if args.descriptors:
            print(f"📖 Lettura descrittori shortcode: {args.descriptors}")
            try:
                with open(args.descriptors, 'r', encoding=args.encoding) as f:
                    shortcode_descriptors = json.load(f)
                print(f"✅ Caricati {len(shortcode_descriptors)} descrittori di shortcode")
            except Exception as e:
                print(f"⚠️ Errore nel caricamento dei descrittori: {str(e)}")
                print("Continuo l'analisi senza descrittori...")

        # Leggi il file
        print(f"📖 Lettura file: {args.input_file}")
        with open(args.input_file, 'r', encoding=args.encoding) as file:
            content = file.read()

        print(f"✅ File letto correttamente ({len(content)} caratteri)")

        # Analizza i tag
        print("🔍 Analisi in corso...")
        analyzer = TagAnalyzer(shortcode_descriptors)
        results = analyzer.analyze_file(content)

        # Stampa i risultati
        analyzer.print_results(results)

        # Gestisci il salvataggio
        output_file = None
        should_save = args.auto_save or args.output

        if args.output:
            output_file = args.output
        elif args.auto_save:
            # Genera nome automaticamente
            input_name = args.input_file.rsplit('.', 1)[0]
            output_file = f"{input_name}_analysis.txt"
        elif not args.auto_save:
            # Chiedi all'utente solo se non è specificato --auto-save
            save_results = input("\nVuoi salvare i risultati in un file? (s/n): ").lower() == 's'
            if save_results:
                custom_name = input("Nome del file (invio per 'tag_analysis_results.txt'): ").strip()
                output_file = custom_name if custom_name else "tag_analysis_results.txt"
                should_save = True

        if should_save and output_file:
            print(f"\n💾 Salvataggio risultati in: {output_file}")
            with open(output_file, 'w', encoding='utf-8') as f:
                # Reindirizza l'output verso il file
                original_stdout = sys.stdout
                sys.stdout = f

                # Aggiungi intestazione al file
                print(f"ANALISI TAG - File: {args.input_file}")
                print(f"Data: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print("=" * 80)

                analyzer.print_results(results)
                sys.stdout = original_stdout

            print(f"✅ Risultati salvati con successo!")

    except FileNotFoundError:
        print(f"❌ File '{args.input_file}' non trovato!")
        print("Controlla che il percorso sia corretto.")
        sys.exit(1)
    except UnicodeDecodeError as e:
        print(f"❌ Errore di codifica nel file '{args.input_file}'")
        print(f"Prova con una codifica diversa usando --encoding (es: --encoding iso-8859-1)")
        print(f"Dettaglio errore: {str(e)}")
        sys.exit(1)
    except PermissionError:
        print(f"❌ Permessi insufficienti per leggere il file '{args.input_file}'")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Errore durante l'analisi: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()