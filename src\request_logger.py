from flask import request, jsonify
import logging
import json
from marshmallow import Schema, fields

class ErrorResponse(Schema):
    status = fields.String(required=True, description="Status of the request", example="error")
    type = fields.String(required=True)
    message = fields.Dict(required=True, description="Error messages")

    @classmethod
    def create(cls, error_type, message):
        data = {"status": "error", "type": error_type, "message": message}
        return cls().dump(data)

def log_request():
    from src.app import logger
    """
    Logs details about incoming API requests.
    """
    if request.is_json:
        data = request.get_json(silent=True)
        if data is None:
            error_response = ErrorResponse.create("json_decode_error", {"error":"Invalid json payload"})
            return jsonify(error_response), 400

    log_data = {
        'client_ip': request.remote_addr,
        'forwarded_ip': request.headers.get('X-Forwarded-For')
    }

    if logger.isEnabledFor(logging.DEBUG):
        log_data['user_agent'] = request.headers.get('User-Agent')
        log_data['method'] = request.method
        log_data['path'] = request.path
        if request.is_json:
            log_data['payload'] = request.json
        logger.debug("API request details: %s", json.dumps(log_data))
    else:
        logger.info("API request details: %s", json.dumps(log_data))