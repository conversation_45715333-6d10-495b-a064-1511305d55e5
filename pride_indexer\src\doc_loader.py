import csv
import logging
from dotenv import load_dotenv
import os
import sys
from azure.search.documents import IndexDocumentsBatch
import json
from collections import Counter
import datetime
from pathlib import Path

# Load environment variables from .env (only for dev/local)
load_dotenv()

# Add the webservices root directory to Python path
webservices_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, webservices_root)

# Now import from utils
from utils.clients import RAGClient, EmbedderClient



# === INPUT OUTPUT CONFIGURATION ===
SHORT_CSV_PATH = os.path.abspath(r"pride_indexer\pride_datasets\_short.csv")
LONG_CSV_PATH = os.path.abspath(r"pride_indexer\pride_datasets\593_long.csv")
UPLOAD_BATCH_SIZE = 100

# === ENHANCED LOGGING CONFIGURATION ===
def setup_enhanced_logging():
    """Configure comprehensive logging to both console and file"""
    
    # Create logs directory if it doesn't exist
    log_dir = Path(webservices_root) / "logs"
    log_dir.mkdir(exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s | %(levelname)8s | %(name)20s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    console_formatter = logging.Formatter(
        '%(asctime)s | %(levelname)s | %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # File handler for comprehensive logging
    file_handler = logging.FileHandler(
        log_dir / "app.log",
        mode='a',
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(detailed_formatter)
    
    # Console handler for immediate feedback
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(console_formatter)
    
    # Add handlers to root logger
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # Suppress verbose Azure SDK logs
    logging.getLogger("azure.core.pipeline.policies.http_logging_policy").setLevel(logging.WARNING)
    logging.getLogger("azure.search.documents").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    return logging.getLogger(__name__)

# Setup enhanced logging
logger = setup_enhanced_logging()



# === SUMMARY LOGGING CLASS ===
class ExecutionSummary:
    """Track and summarize execution metrics for comprehensive logging"""
    
    def __init__(self):
        self.start_time = datetime.datetime.now()
        self.files_processed = 0
        self.files_missing = 0
        self.total_documents = 0
        self.successful_uploads = 0
        self.failed_uploads = 0
        self.errors = []
        self.batch_details = []
        
    def add_file_processed(self, file_type, count):
        self.files_processed += 1
        self.total_documents += count
        
    def add_file_missing(self, file_path):
        self.files_missing += 1
        self.errors.append(f"Missing file: {file_path}")
        
    def add_batch_result(self, label, batch_num, success_count, failed_count):
        self.successful_uploads += success_count
        self.failed_uploads += failed_count
        self.batch_details.append({
            'label': label,
            'batch': batch_num,
            'success': success_count,
            'failed': failed_count
        })
        
    def add_error(self, error_msg):
        self.errors.append(error_msg)
        
    def log_final_summary(self):
        """Log comprehensive execution summary"""
        end_time = datetime.datetime.now()
        duration = end_time - self.start_time
        
        logger.info("=" * 80)
        logger.info(" AZURE AI SEARCH LOADER - EXECUTION SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Execution Time: {duration.total_seconds():.2f} seconds")
        logger.info(f"Files Processed: {self.files_processed}")
        logger.info(f"Files Missing: {self.files_missing}")
        logger.info(f"Total Documents: {self.total_documents}")
        logger.info(f"Successful Uploads: {self.successful_uploads}")
        logger.info(f"Failed Uploads: {self.failed_uploads}")
        
        if self.successful_uploads > 0:
            success_rate = (self.successful_uploads / (self.successful_uploads + self.failed_uploads)) * 100
            logger.info(f"Success Rate: {success_rate:.1f}%")
        
        if self.batch_details:
            logger.info(f"Batch Summary:")
            for batch in self.batch_details:
                logger.info(f"   {batch['label']}: Batch {batch['batch']} - {batch['success']} succeeded, {batch['failed']} failed")
        
        if self.errors:
            logger.info(f"Errors Encountered ({len(self.errors)}):")
            for i, error in enumerate(self.errors[:5], 1):  # Show first 5 errors
                logger.info(f"   {i}. {error}")
            if len(self.errors) > 5:
                logger.info(f"   ... and {len(self.errors) - 5} more errors")
        
        logger.info("=" * 80)

# Initialize execution summary
summary = ExecutionSummary()

# === Enhanced logging helper for batch operations ===
def log_batch_summary(batch_docs, batch_num, label, embedder_size, search_size, success=True, error_msg=None):
    sintcodes = [doc['sIntCode'] for doc in batch_docs]
    total_docs = len(batch_docs)
    unique_sintcodes = len(set(sintcodes))
    duplicate_count = total_docs - unique_sintcodes
    batch_size_bytes = len(json.dumps(batch_docs).encode('utf-8'))
    status = "SUCCESS" if success else "FAILED"
    
    # Enhanced batch logging
    logger.info(f"[{label}] Batch {batch_num}: {total_docs} docs | {unique_sintcodes} unique | "
               f"{duplicate_count} duplicates | {batch_size_bytes:,} bytes | {status}")
    
    if success:
        summary.add_batch_result(label, batch_num, total_docs, 0)
    else:
        summary.add_batch_result(label, batch_num, 0, total_docs)
        summary.add_error(f"Batch {batch_num} failed: {error_msg}")

# === Core ingestion class: prepares and uploads documents in batches ===
class IndexLoader:
    def __init__(self, search_client, embedder_client, batch_size=100, embedding_batch_size=100):
        self.embedder_client = embedder_client
        self.search_client = search_client
        self.batch_size = batch_size
        self.embedding_batch_size = embedding_batch_size

    def validate_vector(self, vector):
        return isinstance(vector, list) and all(isinstance(x, float) for x in vector)

    def prepare_documents(self, rows, description_type, source_lang="en", target_lang="it"):
        """Prepare documents with batch embedding generation"""
        documents = []
        valid_rows = []
        source_texts = []
        target_texts = []

        logger.info(f"Preparing {description_type} documents ({source_lang} → {target_lang})")
        
        # Step 1: Filter and collect valid text fields
        for row in rows:
            if len(row) >= 3:
                valid_rows.append(row)
                source_texts.append(row[1].strip())
                target_texts.append(row[2].strip())

        logger.info(f"Found {len(valid_rows)} valid rows from {len(rows)} total rows")

        if not valid_rows:
            return []

        # Step 2: Generate vector embeddings in batches
        logger.info(f"Generating embeddings for {len(source_texts)} text pairs using batch size: {self.embedding_batch_size}")
        
        # Batch generate source embeddings
        source_vectors = self.embedder_client.generate_embedding_batch(source_texts, batch_size=self.embedding_batch_size)
        
        # Batch generate target embeddings  
        target_vectors = self.embedder_client.generate_embedding_batch(target_texts, batch_size=self.embedding_batch_size)

        logger.info(f"Generated {len(source_vectors)} source and {len(target_vectors)} target embeddings")

        # Step 3: Construct final document objects
        invalid_vectors = 0
        for i, row in enumerate(valid_rows):
            sintcode = row[0].strip()
            source_text = source_texts[i]
            target_text = target_texts[i]
            source_vector = source_vectors[i] if i < len(source_vectors) else []
            target_vector = target_vectors[i] if i < len(target_vectors) else []

            if not self.validate_vector(source_vector) or not self.validate_vector(target_vector):
                logger.error(f"Invalid vector at index {i} for sIntCode {sintcode}")
                invalid_vectors += 1
                continue

            brand = int(row[3].strip()) if description_type == "long" and len(row) > 3 and row[3].strip().isdigit() else None
            type_field = int(row[4].strip()) if description_type == "long" and len(row) > 4 and row[4].strip().isdigit() else None

            documents.append({
                "id": f"{sintcode}_{description_type}_{source_lang}_{target_lang}",
                "sIntCode": sintcode,
                "description_type": description_type,
                "source_lang": source_lang,
                "target_lang": target_lang,
                "source_text": source_text,
                "target_text": target_text,
                "source_text_vector": source_vector,
                "target_text_vector": target_vector,
                "brand": brand,
                "type": type_field,
                "metadata": "",
            })

        if invalid_vectors > 0:
            summary.add_error(f"{invalid_vectors} invalid vectors in {description_type} documents")
            
        logger.info(f"Prepared {len(documents)} {description_type} documents ({invalid_vectors} invalid)")
        summary.add_file_processed(description_type, len(documents))
        
        return documents

    def upload_documents_in_batches(self, documents, label="DOCS"):
        """Upload with enhanced progress tracking and error handling"""
        if not documents:
            logger.warning(f"No valid {label.lower()} documents to upload")
            return

        logger.info(f"Starting upload of {len(documents)} {label} documents...")

        # Check for duplicates
        sintcode_counts = Counter(doc['sIntCode'] for doc in documents)
        duplicates = [k for k, v in sintcode_counts.items() if v > 1]
        
        if duplicates:
            logger.warning(f"Found {len(duplicates)} duplicate sIntCodes in {label}")
            logger.warning(f"Duplicate sIntCodes: {', '.join(duplicates)}")
            # Log detailed duplicate information
            for sintcode in duplicates:
                count = sintcode_counts[sintcode]
                logger.warning(f"   sIntCode '{sintcode}' appears {count} times")
            summary.add_error(f"Duplicates in {label}: {len(duplicates)} sIntCodes - {', '.join(duplicates)}")

        # Upload in batches
        total_uploaded = 0
        for batch_num, i in enumerate(range(0, len(documents), self.batch_size), 1):
            batch_docs = documents[i:i+self.batch_size]
            batch = IndexDocumentsBatch()
            batch.add_upload_actions(batch_docs)
            
            try:
                self.upload_batch_to_search(batch)
                total_uploaded += len(batch_docs)
                log_batch_summary(batch_docs, batch_num, label, len(batch_docs), len(batch_docs), success=True)
                
                if batch_num == 1:
                    sample_id = batch_docs[0]['id']
                    logger.info(f"Sample document ID: {sample_id}")
                    
            except Exception as e:
                logger.error(f"Batch {batch_num} failed: {str(e)}")
                log_batch_summary(batch_docs, batch_num, label, len(batch_docs), len(batch_docs), success=False, error_msg=str(e))

        logger.info(f"{label} upload completed: {total_uploaded}/{len(documents)} documents")

    def upload_batch_to_search(self, batch):
        if not batch or not batch.actions:
            raise ValueError("Empty batch provided")
        self.search_client.search_documents_client.index_documents(batch)

# === Main execution with enhanced logging ===
if __name__ == "__main__":
    logger.info("AZURE AI SEARCH DOCUMENT LOADER STARTED")
    logger.info("=" * 80)
    
    # Configuration with batch sizes
    SOURCE_LANGUAGE = "en"
    TARGET_LANGUAGE = "it"
    EMBEDDING_BATCH_SIZE = 100  # Batch size for Azure OpenAI embeddings
    SEARCH_BATCH_SIZE = 100     # Batch size for Azure Search uploads
    
    logger.info(f"Language Pair: {SOURCE_LANGUAGE} → {TARGET_LANGUAGE}")
    logger.info(f"ID Format: {{sintcode}}_{{type}}_{SOURCE_LANGUAGE}_{TARGET_LANGUAGE}")
    logger.info(f"Embedding Batch Size: {EMBEDDING_BATCH_SIZE}")
    logger.info(f"Search Batch Size: {SEARCH_BATCH_SIZE}")
    logger.info(f"Short CSV: {SHORT_CSV_PATH}")
    logger.info(f"Long CSV: {LONG_CSV_PATH}")

    try:
        # Initialize clients with batch configuration
        logger.info("Initializing Azure clients...")
        embedder_client = EmbedderClient()
        rag_client = RAGClient()
        ingestor = IndexLoader(
            rag_client, 
            embedder_client, 
            batch_size=SEARCH_BATCH_SIZE,
            embedding_batch_size=EMBEDDING_BATCH_SIZE
        )
        logger.info("Azure clients initialized with batch processing")

        # Process short descriptions
        if os.path.isfile(SHORT_CSV_PATH):
            logger.info("Processing short descriptions...")
            with open(SHORT_CSV_PATH, encoding="utf-8") as f:
                reader = csv.reader(f)
                next(reader, None)
                short_rows = list(reader)
            
            short_documents = ingestor.prepare_documents(
                short_rows, "short", SOURCE_LANGUAGE, TARGET_LANGUAGE
            )
            ingestor.upload_documents_in_batches(short_documents, label="SHORT")
        else:
            logger.warning(f"Short CSV file not found: {SHORT_CSV_PATH}")
            summary.add_file_missing(SHORT_CSV_PATH)

        # Process long descriptions
        if os.path.isfile(LONG_CSV_PATH):
            logger.info("Processing long descriptions...")
            with open(LONG_CSV_PATH, encoding="utf-8") as f:
                reader = csv.reader(f)
                next(reader, None)
                long_rows = list(reader)
            
            long_documents = ingestor.prepare_documents(
                long_rows, "long", SOURCE_LANGUAGE, TARGET_LANGUAGE
            )
            ingestor.upload_documents_in_batches(long_documents, label="LONG")
        else:
            logger.warning(f"Long CSV file not found: {LONG_CSV_PATH}")
            summary.add_file_missing(LONG_CSV_PATH)

    except Exception as e:
        logger.error(f"Critical error: {str(e)}", exc_info=True)
        summary.add_error(f"Critical error: {str(e)}")
    
    finally:
        # Log final summary
        summary.log_final_summary()
        logger.info("🏁 AZURE AI SEARCH DOCUMENT LOADER COMPLETED")