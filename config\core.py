import logging
from configparser import RawConfigParser
from getpass import getuser
from os import environ, getlogin
from pathlib import Path
from utils.core import get_relative_path
from utils.patterns import Singleton

DEFAULT_CONFIG_FILE = Path(__file__).resolve().parent.parent / "config.ini"

user = getuser().lower()


class PrimaryConfig(metaclass=Singleton):
    def __init__(self) -> None:
        # Default config file
        config_custom = "config.ini"

        # Check overrides from local config_xyz.ini files
        env = environ.get("FLASK_ENV")
        # THIS GENERATES AN ERROR ON APP SERVICE!!!
        #username = getlogin().lower()

        if env == "PRD":
            config_custom = "config_prd.ini"
        elif env == "STG":
            config_custom = "config_stg.ini"
        elif env == "DEV":
            config_custom = "config_dev.ini"

        self.config = RawConfigParser(allow_no_value=True)
        self.config.read([
            DEFAULT_CONFIG_FILE,                            # path/to/file/config.ini
            DEFAULT_CONFIG_FILE.with_name(config_custom)    # path/to/file/config_xyz.ini
            ])


class SubordinateConfig:
    def __init__(self, module: str) -> None:
        self.__module = module
        self.primary_config = PrimaryConfig()

    def get_setting(self, name: str, is_in_env: bool = False) -> str:
        vars = environ if is_in_env else None
        return self.primary_config.config.get(self.__module, name, vars=vars)


class TranslateConfig(metaclass=Singleton):
    def __init__(self) -> None:
        self.text = SubordinateConfig("text_translation")
        self.document = SubordinateConfig("document_translation")
        self.common = SubordinateConfig("common")
        print(self)

    @property
    def text_endpoint(self) -> str:
        return self.text.get_setting("endpoint")

    @property
    def document_endpoint(self) -> str:
        return self.document.get_setting("endpoint")

    @property
    def translator_key(self) -> str:
        return self.common.get_setting("TRANSLATOR_KEY", True)

    @property
    def region(self) -> str:
        return self.common.get_setting("region")

    @property
    def llm_endpoint(self) -> str:
        return self.common.get_setting("llm_endpoint")

    @property
    def llm_key(self) -> str:
        return self.common.get_setting("LLM_KEY", True)

    @property
    def api_version(self) -> str:
        return self.common.get_setting("api_version")

    @property
    def app_static_version(self) -> str:
        return self.common.get_setting("app_static_version")

    @property
    def log_level(self) -> str:
        return getattr(logging, self.common.get_setting("log_level").upper(), logging.INFO)

    @property
    def llm_name(self) -> str:
        return self.common.get_setting("llm_name")

    ## Embedder properties and AI Search properties
    @property
    def embedder_endpoint(self) -> str:
        return self.common.get_setting("embedder_endpoint")

    @property
    def embedder_key(self) -> str:
        return self.common.get_setting("embedder_key", True)

    @property
    def embedder_version(self) -> str:
        return self.common.get_setting("embedder_version")

    @property
    def embedder_deployment_name(self) -> str:
        return self.common.get_setting("embedder_deployment_name")

    @property
    def search_endpoint(self) -> str:
        return self.common.get_setting("search_endpoint")

    @property
    def search_key(self) -> str:
        return self.common.get_setting("search_key", True)

    @property
    def search_index(self) -> str:
        return self.common.get_setting("search_index")

    # @property
    # def custom_translator_endpoint(self) -> str:
    #     return self.common.get_setting("custom_translator_endpoint")

    # @property
    # def custom_translator_key(self) -> str:
    #     return self.common.get_setting("custom_translator_key", True)

    # @property
    # def custom_translator_region(self) -> str:
    #     return self.common.get_setting("custom_translator_region")

    # @property
    # def custom_translator_category(self) -> str:
    #     return self.common.get_setting("custom_translator_category")



    def __str__(self) -> str:
        """ Mostra solo i primi 5 caratteri della chiave"""
        def safe_key_display(key_value):  # Mostra solo i primi 5 caratteri della chiave
            """Safely display key with first 5 characters"""
            return f"{key_value[:5]}..." if key_value and len(key_value) > 5 else "None..."

        return (
            f"TranslateConfig(\n"
            f"  text_endpoint: {self.text_endpoint}\n"
            f"  document_endpoint: {self.document_endpoint}\n"
            f"  translator_key: {safe_key_display(self.translator_key)}\n"
            f"  region: {self.region}\n"
            f"  llm_endpoint: {self.llm_endpoint}\n"
            f"  llm_key: {safe_key_display(self.llm_key)}\n"
            f"  api_version: {self.api_version}\n"
            f"  app_static_version: {self.app_static_version}\n"
            f"  llm_name: {self.llm_name}\n"
            f"  embedder_endpoint: {self.embedder_endpoint or 'None'}\n"
            f"  embedder_key: {safe_key_display(self.embedder_key)}\n"
            f"  embedder_version: {self.embedder_version or 'None'}\n"
            f"  embedder_deployment_name: {self.embedder_deployment_name or 'None'}\n"
            f"  search_endpoint: {self.search_endpoint or 'None'}\n"
            f"  search_key: {safe_key_display(self.search_key)}\n"
            f"  search_index: {self.search_index or 'None'}\n"
            # f"  custom_translator_endpoint: {self.custom_translator_endpoint or 'None'}\n"
            # f"  custom_translator_key: {safe_key_display(self.custom_translator_key)}\n"
            # f"  custom_translator_region: {self.custom_translator_region or 'None'}\n"
            # f"  custom_translator_category: {self.custom_translator_category or 'None'}\n"
            f")"
        )
