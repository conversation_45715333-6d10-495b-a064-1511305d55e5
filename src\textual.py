import re
from concurrent.futures import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>xecutor
from typing import Dict, List

from azure.core.credentials import AzureKeyCredential
from azure.ai.translation.text import TextTranslationClient
from azure.ai.translation.text.models import InputTextItem, TranslatedTextItem, TextType
from azure.core.exceptions import HttpResponseError

from config.core import TranslateConfig
from utils.clients import ProofreaderClient, RAGClient
from utils.core import ClientException, Language, LocalizedText, Mode, Translator, get_logger
from utils.patterns import Singleton

logger = get_logger(__file__)

DO_NOT_TRANSLATE = [
    "SkyLine",
    "SkyDuo",
    "Cook&Chill",
    "Electrolux Professional Chef Academy",
    "SkyLine PremiumS",
    "SkyLine Premium",
    "SkyLine ProS",
    "SkyLine Pro",
    "ENERGY STAR",
    "Energy Technology List",
    "OnE Connected",
    "Low Temperature Cooking",
    "SkyClean",
    "Cycles+",
    "YouTube",
    "Smart Steam Crosswise",
    "Crosswise",
    "GourmeXpress",
    "MultiSlim",
    "thermaline",
    "Red Dot Award",
    "Free-cooking",
    "ProThermetic",
    "AquaCooker",
    "eXPerience",
    "900XP",
    "700XP",
    "700&900XP",
    "EcoFlame",
    "ErgoCert",
    "Stress-free",
    "NeoBlue Touch",
    "OnE Connected App",
    "MyEco",
    "CO2",
    "green&clean",
    "WASH·SAFE CONTROL",
    "HeroDry",
    "Non-stop ",
    "ZeroLime device",
    "Energy Savings Device",
    "ESD ",
    "SkyLine Combi",
    "SpeeDelight",
    "Chef Academy",
    "Energy Technology List (ETL)",
    "Food Safe Control",
    "Plan-n-Save",
    "OptiFlow",
    "Lambda",
    "SkyClean",
    "GourmeXpress",
    "Energy Saving Mode",
    "MultiSlim",
    "TrinityPro",
    "TRS",
    "TRK",
    "K",
    "ecostoreHP",
    "SafeBox Hold",
    "Flexy Line",
    "LED",
    "Flexy Compact & CompactJR",
    "Flexy Drop-in",
    "NitroChrome3",
    "700XP and 900XP",
    "XP Electric PowerGrillHP",
    "PowerGrillHP",
    "SpeeDelight PEP",
    "Triple Play",
    "Perfect Squeeze",
    "Good Design Award",
    "Plus X Award",
    "Janus",
    "HPe",
    "Frost Watch Control",
    "Haccp",
    "Prostore",
    "R290",
    "CO2",
    "ETL",
    "HP",
    "Wine Line",
    "UV",
    "Red Dot Award",
    "Saladette",
    "BPA-free",
    "Turboliquidizers",
    "AISI 304",
    "GN",
    "Simplicity Bubblers",
    "I-Tank",
    "I-PRO",
    "SP",
    "GT",
    "USPHS",
    "EU Ecolabel",
    "Nordic Swan Ecolabel",
    "Essentia",
    "ClarusVibe",
    "Clarus Vibe",
    "Compass Pro",
    "Automatic Savings",
    "Integrated Savings",
    "Integlligent Dosing",
    "Efficient Dosing",
    "Power Balance",
    "Multisave",
    "Dosave",
    "Jetsave",
    "Moisture Balance",
    "Drum Speed",
    "Adaptive Fan",
    "lagoon Advance Care",
    "Woolmark",
    "The Woolmark Company",
    "Hygiene Watchdog",
    "Auto Inner Door Opening",
    "Line 6000",
    "Line 6000 Pocket",
    "Line 6000 Evolution",
    "Line 6000 Hyevolution",
    "Line 6000 Pullman",
    "DIAMMS™",
    "FoldFlex",
    "Hygiene Guard",
    "Feed Now",
    "Dubixium",
    "Feed Alone",
    "FFS Vibe",
    "myPRO",
    "myPRO XL",
    "myPROzip",
    "Automatic Moisture Control",
    "SpeedCare Drum",
    "Certus Management Information System",
    "CMIS",
    "myLaundry Concept Store",
]


class TextTranslator(Translator, metaclass=Singleton):

    def __init__(self, verify=True) -> None:
        self.proofreader = ProofreaderClient(verify=verify)
        self.retriever = RAGClient(verify=verify)
        config = TranslateConfig()

        # Standard translator
        self.translator = TextTranslationClient(
            endpoint=config.text_endpoint,
            credential=AzureKeyCredential(config.translator_key),
            region=config.region,
        )

        logger.info(f"KEY: {config.region} {config.translator_key}")


        # Custom translator
        # self.custom_translator = TextTranslationClient(
        #     endpoint=config.custom_translator_endpoint,
        #     credential=AzureKeyCredential(config.custom_translator_key),
        #     region=config.custom_translator_region,
        # )

        # self.custom_category = config.custom_translator_category

    @property
    def supported_languages(self) -> List[Language]:
        try:
            supported_languages = self.translator.get_supported_languages(
                scope="translation"
            ).translation
        except HttpResponseError as e:
            raise ClientException(
                "Unable to retrieve supported languages.", e.status_code
            )

        if supported_languages is not None:
            return [
                Language(code, description)
                for code, description in supported_languages.items()
            ]
        else:
            raise ClientException("No languages supported.", 500)


    def translate(
        self,
        texts: Dict[str, str],
        source: str,
        target: List[str],
        description_type: str = None,
        brand: str = None,
        type_: str = None,
        mode: Mode = Mode.STANDARD_TURBO,
        text_type: TextType = TextType.PLAIN
    ) -> Dict[str, Dict[str, str]]:

        # Protect words that should not be translated
        logger.debug(f"Translate with text type: {text_type}")
        if mode == Mode.STANDARD_PROOFREAD:
            processed_texts = {k: protect_no_translate_words(v) for k, v in texts.items()}
        else:
            processed_texts = texts

        to_translate = [InputTextItem(text=text) for text in processed_texts.values()]

        # Handle different translation services based on mode
        if mode == Mode.STANDARD_CUSTOM:
            # try:
            #     response: List[TranslatedTextItem] = self.custom_translator.translate(
            #         body=to_translate,
            #         to_language=target,
            #         from_language=source,
            #         text_type=text_type,
            #         category=self.custom_category
            #     )
            # except HttpResponseError as e:
            #     raise ClientException(e.message, e.status_code)
            pass
        else:
            try:
                translate_kwargs = {
                    "body": to_translate,
                    "to_language": target,
                    "text_type": text_type,
                }
                if source != "auto":
                    translate_kwargs["from_language"] = source

                response: List[TranslatedTextItem] = self.translator.translate(
                    **translate_kwargs
                )
            except HttpResponseError as e:
                raise ClientException(e.message, e.status_code)

        texts_translations = {}
        executor = ThreadPoolExecutor(len(texts))
        supported_languages = {
            language.code: language.description for language in self.supported_languages
        }

        if response:
            # Convert keys to list for indexing
            text_ids = list(texts.keys())

            for idx, (id, translated_text) in enumerate(zip(text_ids, response)):
                texts_translations[id] = {}

                if source == "auto":
                    # extract source language from response (auto-detected)
                    source_for_single_text = translated_text.detected_language.language
                else:
                    source_for_single_text = source

                source_texts = [
                    LocalizedText(
                        texts[id],
                        Language(source_for_single_text, supported_languages[source_for_single_text])
                    ) for _ in translated_text.translations
                ]

                target_texts = [
                    LocalizedText(
                        translation.text,
                        Language(
                            translation.to,
                            supported_languages[translation.to]
                        )
                    ) for translation in translated_text.translations
                ]


                match mode:
                    case Mode.STANDARD_PROOFREAD:
                        def proofread_wrapper(source_lt, target_lt):
                            try:
                                return self.proofreader.review_translation(source_lt, target_lt)
                            except Exception as e:
                                logger.error("Proofreader failed", exc_info=True)
                                return target_lt

                        reviewed_translations = executor.map(
                            proofread_wrapper,
                            source_texts,
                            target_texts,
                        )


                    case Mode.STANDARD_TURBO | Mode.STANDARD_CUSTOM:
                        reviewed_translations = reviewed_translations = target_texts

                    case Mode.STANDARD_RAG:
                        def rag_wrapper(source_lt, target_lt):
                            try:
                                return self.retriever.rag_review_translation(
                                    source = source_lt,
                                    target = target_lt,
                                    description_type=description_type,
                                    brand=brand,
                                    type_=type_,
                                )
                            except Exception as e:
                                logger.error(f"RAG translation failed for input [{source_lt.text[:30]}...]: {e}")
                                return target_lt
                        try:
                            reviewed_translations = list(executor.map(
                                rag_wrapper,
                                source_texts,
                                target_texts,
                            ))
                        except Exception as e:
                                logger.exception("RAG wrapper failed:")
                                raise

                for translation in reviewed_translations:
                    translated_text = translation.text
                    if mode == Mode.STANDARD_PROOFREAD:
                        translated_text = remove_notranslate_spans(translated_text)
                    texts_translations[id][translation.language.code] = translated_text

        return texts_translations


def protect_no_translate_words(text: str) -> str:
    """
    Racchiude le parole da non tradurre in tag <span class="notranslate">...</span>
    evitando annidamenti in caso di parole simili.
    """
    # Ordina per lunghezza decrescente per matchare prima le parole più lunghe
    words = sorted(DO_NOT_TRANSLATE, key=len, reverse=True)
    # Costruisci una regex che matcha tutte le parole/frasi come parola intera
    # Usa word boundaries per evitare match parziali di singole lettere
    escaped_words = []
    for w in words:
        if len(w) == 1 and w.isalpha():
            # Per singole lettere, usa word boundaries per matchare solo parole complete
            escaped_words.append(r'\b' + re.escape(w) + r'\b')
        else:
            # Per parole/frasi più lunghe, usa il normale escape
            escaped_words.append(re.escape(w))

    pattern = r'(' + '|'.join(escaped_words) + r')'

    # Trova tutte le occorrenze senza sovrapposizioni
    matches = list(re.finditer(pattern, text))
    if not matches:
        return text

    # Costruisci il nuovo testo proteggendo solo le occorrenze trovate
    result = []
    last_index = 0
    for m in matches:
        start, end = m.span()
        # Salta match che si trova già dentro un tag notranslate
        if '<span class="notranslate">' in text[max(0, start-25):start]:
            continue
        result.append(text[last_index:start])
        result.append(f'<span class="notranslate">{m.group(0)}</span>')
        last_index = end
    result.append(text[last_index:])
    return ''.join(result)


def remove_notranslate_spans(text: str) -> str:
    """
    Rimuove solo i tag <span class="notranslate">...</span> lasciando il contenuto interno.
    Gli altri tag <span> non vengono toccati.
    Gestisce sia virgolette dritte che curve.
    """
    # Handle different quote variations more robustly
    text = re.sub(r'<span\s+class=.notranslate.>(.*?)</span>', r'\1', text, flags=re.DOTALL | re.IGNORECASE)

    return text