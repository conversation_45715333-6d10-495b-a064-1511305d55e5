#!/usr/bin/env python3
"""
Test to verify that true batch processing is now working.
This should show "Starting batch proofreading for X texts" where X > 1.
"""

import sys
import os
import logging

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.textual import TextTranslator
from utils.core import Mode

# Configure logging to see the batch processing messages
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

def test_true_batch_processing():
    """Test that batch processing actually processes multiple texts in one call."""
    
    print("=" * 60)
    print("TRUE BATCH PROCESSING TEST")
    print("=" * 60)
    
    # Create test data with multiple texts
    texts_to_translate = {
        "text1": "Hello world",
        "text2": "This is a test",
        "text3": "Electrolux Professional equipment",
        "text4": "Another text to translate",
        "text5": "Final test text"
    }
    
    print(f"Testing with {len(texts_to_translate)} texts")
    print("Look for: 'Starting batch proofreading for 5 texts' (not 5 separate calls)")
    print("-" * 60)
    
    try:
        translator = TextTranslator(verify=False)
        
        # This should now show "Starting batch proofreading for 5 texts"
        result = translator.translate(
            texts_to_translate,
            source="en",
            target=["de"],
            mode=Mode.STANDARD_PROOFREAD_BATCH
        )
        
        print("-" * 60)
        print("✅ Translation completed!")
        print(f"Translated {len(result)} texts")
        
        # Show results
        for text_id, translations in result.items():
            for lang, translation in translations.items():
                print(f"  {text_id} ({lang}): '{translation}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_vs_batch_comparison():
    """Compare individual vs batch to see the difference."""
    
    print("\n" + "=" * 60)
    print("INDIVIDUAL VS BATCH COMPARISON")
    print("=" * 60)
    
    texts = {
        "text1": "Hello world",
        "text2": "This is a test",
        "text3": "Final text"
    }
    
    translator = TextTranslator(verify=False)
    
    print("🔍 INDIVIDUAL MODE:")
    print("Should see 3 separate processing messages")
    print("-" * 30)
    
    try:
        result_individual = translator.translate(
            texts,
            source="en", 
            target=["de"],
            mode=Mode.STANDARD_PROOFREAD  # Individual mode
        )
        print("✅ Individual mode completed")
    except Exception as e:
        print(f"❌ Individual mode failed: {e}")
    
    print("\n🔍 BATCH MODE:")
    print("Should see 1 batch processing message for 3 texts")
    print("-" * 30)
    
    try:
        result_batch = translator.translate(
            texts,
            source="en",
            target=["de"], 
            mode=Mode.STANDARD_PROOFREAD_BATCH  # Batch mode
        )
        print("✅ Batch mode completed")
    except Exception as e:
        print(f"❌ Batch mode failed: {e}")

if __name__ == "__main__":
    print("🧪 Testing True Batch Processing")
    print("This test verifies that the batch processing fix is working correctly.\n")
    
    # Test 1: True batch processing
    success1 = test_true_batch_processing()
    
    # Test 2: Comparison
    test_individual_vs_batch_comparison()
    
    print("\n" + "=" * 60)
    if success1:
        print("🎉 TRUE BATCH PROCESSING IS NOW WORKING!")
        print("You should have seen 'Starting batch proofreading for X texts' where X > 1")
    else:
        print("❌ Batch processing test failed")
    
    print("\nKey indicators of success:")
    print("✅ 'Starting batch proofreading for X texts' where X > 1")
    print("✅ Only ONE batch processing message per translate() call")
    print("❌ Multiple 'Starting batch proofreading for 1 texts' messages")
