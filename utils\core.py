from abc import abstractmethod
from enum import Enum, auto
from logging import Logger, getLogger
from os.path import basename, dirname, isfile, join
from typing import Any, List

from azure.ai.translation.text.models import TranslationLanguage


def get_logger(file_path: str) -> Logger:
    """Returns a logger for the specified file.

    Args:
        file_path (str): The path of the file that must be logged.

    Returns:
        Logger: The logger for the give file.
    """
    assert isfile(file_path), "Must log an existing file."
    return getLogger(basename(dirname(file_path)))


def get_relative_path(base_file_path: str, relative_path: str) -> str:
    return join(dirname(base_file_path), relative_path)


class ClientException(Exception):
    def __init__(self, message: str, status: int, *args: object) -> None:
        super().__init__(*args)

        self.__message = message
        self.__status = status

    @property
    def message(self) -> str:
        return self.__message

    @property
    def status(self) -> int:
        return self.__status


class Language:
    def __init__(self, code, description) -> None:
        self.__code = code
        self.__description = description

    @property
    def code(self) -> str:
        return self.__code

    @property
    def description(self) -> TranslationLanguage:
        return self.__description


class LocalizedText:
    def __init__(self, text: str, language: Language, description_type: str = None, brand: str = None, type_: str = None) -> None:
        self.__text = text
        self.__language = language
        self.__description_type = description_type
        self.__brand = brand
        self.__type = type_

    @property
    def text(self) -> str:
        return self.__text

    @property
    def language(self) -> Language:
        return self.__language

    @property
    def description_type(self) -> str:
        return self.__description_type

    @property
    def brand(self) -> int:
        return self.__brand

    @property
    def type(self) -> int:
        return self.__type


class Translator:

    @property
    @abstractmethod
    def supported_languages(self) -> List[Language]:
        return

    @abstractmethod
    def translate(self, content, source, target) -> Any:
        return


class Mode(Enum):
    STANDARD_TURBO = auto()
    STANDARD_PROOFREAD = auto()
    STANDARD_RAG = auto()
    STANDARD_CUSTOM = auto()


